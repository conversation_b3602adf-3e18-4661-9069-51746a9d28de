<?xml version="1.0" encoding="UTF-8" ?>
<Page
    x:Class="DinPresto.Views.ClientesPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="using:DinPresto.Models"
    mc:Ignorable="d">

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" Text="👥 Gestión de Clientes" FontSize="28" FontWeight="Bold" VerticalAlignment="Center"/>
            <Button Grid.Column="1" Content="➕ Nuevo Cliente" Background="{ThemeResource SystemAccentColor}"
                   Foreground="White" Padding="15,10" CornerRadius="5" Command="{x:Bind ViewModel.ShowNuevoClienteFormCommand}"/>
        </Grid>

        <!-- Search and Filters -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20" BorderBrush="LightGray" BorderThickness="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Grid.Column="0" PlaceholderText="🔍 Buscar por nombre, documento o teléfono..."
                        Text="{x:Bind ViewModel.SearchText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        Margin="0,0,10,0" VerticalAlignment="Center"/>
                <ComboBox Grid.Column="1" PlaceholderText="Tipo Documento" Width="150" Margin="0,0,10,0"
                         SelectedItem="{x:Bind ViewModel.SelectedTipoDocumento, Mode=TwoWay}">
                    <x:Null/>
                    <models:TipoDocumento>Cedula</models:TipoDocumento>
                    <models:TipoDocumento>Pasaporte</models:TipoDocumento>
                    <models:TipoDocumento>RNC</models:TipoDocumento>
                    <models:TipoDocumento>Otro</models:TipoDocumento>
                </ComboBox>
                <Button Grid.Column="2" Content="🔍 Buscar" Padding="15,8" Command="{x:Bind ViewModel.SearchClientesCommand}"/>
            </Grid>
        </Border>

        <!-- Clients List -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" BorderBrush="LightGray" BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- List Header -->
                <Border Grid.Row="0" Background="LightGray" Padding="15,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Cliente" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="1" Text="Documento" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="2" Text="Teléfono" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="3" Text="Préstamos" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="4" Text="Deuda Total" FontWeight="SemiBold"/>
                        <TextBlock Grid.Column="5" Text="Acciones" FontWeight="SemiBold"/>
                    </Grid>
                </Border>

                <!-- List Content -->
                <ListView Grid.Row="1" Padding="0" ItemsSource="{x:Bind ViewModel.ClientesFiltrados, Mode=OneWay}">
                    <ListView.ItemTemplate>
                        <DataTemplate x:DataType="models:Cliente">
                            <Grid Padding="15,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="{x:Bind NombreCompleto}" FontWeight="SemiBold"/>
                                    <TextBlock Text="{x:Bind Email}" FontSize="12" Foreground="Gray"/>
                                </StackPanel>
                                <TextBlock Grid.Column="1" Text="{x:Bind DocumentoIdentidad}" VerticalAlignment="Center"/>
                                <TextBlock Grid.Column="2" Text="{x:Bind Telefono}" VerticalAlignment="Center"/>
                                <TextBlock Grid.Column="3" Text="{x:Bind TotalPrestamos}" VerticalAlignment="Center"/>
                                <TextBlock Grid.Column="4" Text="{x:Bind TotalDeuda, StringFormat='C2'}" VerticalAlignment="Center"
                                          Foreground="{x:Bind TotalDeuda, Converter={StaticResource DeudaColorConverter}}"/>

                                <StackPanel Grid.Column="5" Orientation="Horizontal" Spacing="5">
                                    <Button Content="👁️" ToolTipService.ToolTip="Ver detalles" Padding="8"/>
                                    <Button Content="✏️" ToolTipService.ToolTip="Editar" Padding="8"/>
                                    <Button Content="🗑️" ToolTipService.ToolTip="Eliminar" Padding="8" Foreground="Red"/>
                                </StackPanel>
                            </Grid>
                        </DataTemplate>
                    </ListView.ItemTemplate>


                </ListView>
            </Grid>
        </Border>
    </Grid>
</Page>
