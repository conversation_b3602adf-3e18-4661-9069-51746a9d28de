using DinPresto.Data;
using DinPresto.Models;
using Microsoft.EntityFrameworkCore;

namespace DinPresto.Services
{
    public class ClienteService : IClienteService
    {
        private readonly DinPrestoContext _context;

        public ClienteService(DinPrestoContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Cliente>> GetAllClientesAsync()
        {
            return await _context.Clientes
                .Include(c => c.Prestamos)
                .OrderBy(c => c.Nombre)
                .ThenBy(c => c.Apellidos)
                .ToListAsync();
        }

        public async Task<IEnumerable<Cliente>> GetClientesActivosAsync()
        {
            return await _context.Clientes
                .Include(c => c.Prestamos)
                .Where(c => c.Activo)
                .OrderBy(c => c.Nombre)
                .ThenBy(c => c.Apellidos)
                .ToListAsync();
        }

        public async Task<Cliente?> GetClienteByIdAsync(int id)
        {
            return await _context.Clientes
                .Include(c => c.Prestamos)
                .ThenInclude(p => p.Cuotas)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<Cliente?> GetClienteByDocumentoAsync(string documento)
        {
            return await _context.Clientes
                .Include(c => c.Prestamos)
                .FirstOrDefaultAsync(c => c.DocumentoIdentidad == documento);
        }

        public async Task<Cliente> CreateClienteAsync(Cliente cliente)
        {
            // Validaciones de negocio
            if (await ExisteDocumentoAsync(cliente.DocumentoIdentidad))
            {
                throw new InvalidOperationException("Ya existe un cliente con este documento de identidad.");
            }

            cliente.FechaRegistro = DateTime.Now;
            cliente.Activo = true;

            _context.Clientes.Add(cliente);
            await _context.SaveChangesAsync();

            return cliente;
        }

        public async Task<Cliente> UpdateClienteAsync(Cliente cliente)
        {
            // Validaciones de negocio
            if (await ExisteDocumentoAsync(cliente.DocumentoIdentidad, cliente.Id))
            {
                throw new InvalidOperationException("Ya existe otro cliente con este documento de identidad.");
            }

            var clienteExistente = await _context.Clientes.FindAsync(cliente.Id);
            if (clienteExistente == null)
            {
                throw new InvalidOperationException("Cliente no encontrado.");
            }

            // Actualizar propiedades
            clienteExistente.Nombre = cliente.Nombre;
            clienteExistente.Apellidos = cliente.Apellidos;
            clienteExistente.DocumentoIdentidad = cliente.DocumentoIdentidad;
            clienteExistente.TipoDocumento = cliente.TipoDocumento;
            clienteExistente.Email = cliente.Email;
            clienteExistente.Telefono = cliente.Telefono;
            clienteExistente.Direccion = cliente.Direccion;
            clienteExistente.Activo = cliente.Activo;

            await _context.SaveChangesAsync();

            return clienteExistente;
        }

        public async Task<bool> DeleteClienteAsync(int id)
        {
            if (!await CanDeleteClienteAsync(id))
            {
                return false;
            }

            var cliente = await _context.Clientes.FindAsync(id);
            if (cliente == null)
            {
                return false;
            }

            // Eliminación lógica - marcar como inactivo
            cliente.Activo = false;
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<bool> CanDeleteClienteAsync(int id)
        {
            // Verificar si tiene préstamos activos
            var tienePrestamosActivos = await _context.Prestamos
                .AnyAsync(p => p.ClienteId == id && p.Estado == EstadoPrestamo.Activo);

            return !tienePrestamosActivos;
        }

        public async Task<IEnumerable<Cliente>> SearchClientesAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetClientesActivosAsync();
            }

            var searchLower = searchTerm.ToLower();

            return await _context.Clientes
                .Include(c => c.Prestamos)
                .Where(c => c.Activo && (
                    c.Nombre.ToLower().Contains(searchLower) ||
                    c.Apellidos.ToLower().Contains(searchLower) ||
                    c.DocumentoIdentidad.Contains(searchTerm) ||
                    (c.Email != null && c.Email.ToLower().Contains(searchLower)) ||
                    (c.Telefono != null && c.Telefono.Contains(searchTerm))
                ))
                .OrderBy(c => c.Nombre)
                .ThenBy(c => c.Apellidos)
                .ToListAsync();
        }

        public async Task<IEnumerable<Cliente>> GetClientesByTipoDocumentoAsync(TipoDocumento tipoDocumento)
        {
            return await _context.Clientes
                .Include(c => c.Prestamos)
                .Where(c => c.Activo && c.TipoDocumento == tipoDocumento)
                .OrderBy(c => c.Nombre)
                .ThenBy(c => c.Apellidos)
                .ToListAsync();
        }

        public async Task<bool> ExisteDocumentoAsync(string documento, int? excludeClienteId = null)
        {
            var query = _context.Clientes.Where(c => c.DocumentoIdentidad == documento);

            if (excludeClienteId.HasValue)
            {
                query = query.Where(c => c.Id != excludeClienteId.Value);
            }

            return await query.AnyAsync();
        }

        public async Task<decimal> GetTotalDeudaClienteAsync(int clienteId)
        {
            return await _context.Prestamos
                .Where(p => p.ClienteId == clienteId && p.Estado == EstadoPrestamo.Activo)
                .SumAsync(p => p.SaldoPendiente);
        }

        public async Task<int> GetTotalPrestamosClienteAsync(int clienteId)
        {
            return await _context.Prestamos
                .CountAsync(p => p.ClienteId == clienteId);
        }

        public async Task<IEnumerable<Cliente>> GetClientesMorososAsync()
        {
            var fechaLimite = DateTime.Now.AddDays(-30); // Considerar moroso después de 30 días

            return await _context.Clientes
                .Include(c => c.Prestamos)
                .ThenInclude(p => p.Cuotas)
                .Where(c => c.Activo && c.Prestamos.Any(p => 
                    p.Estado == EstadoPrestamo.Activo && 
                    p.Cuotas.Any(cu => cu.Estado == EstadoCuota.Vencida && cu.FechaVencimiento < fechaLimite)))
                .OrderBy(c => c.Nombre)
                .ThenBy(c => c.Apellidos)
                .ToListAsync();
        }

        public async Task<IEnumerable<Cliente>> GetMejoresClientesAsync(int top = 10)
        {
            return await _context.Clientes
                .Include(c => c.Prestamos)
                .Where(c => c.Activo)
                .OrderByDescending(c => c.Prestamos.Sum(p => p.MontoPagado))
                .ThenByDescending(c => c.Prestamos.Count)
                .Take(top)
                .ToListAsync();
        }
    }
}
