<?xml version="1.0" encoding="UTF-8" ?>
<Page
    x:Class="DinPresto.Views.ClienteFormPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="using:DinPresto.Models"
    mc:Ignorable="d">

    <ScrollViewer Padding="20">
        <StackPanel Spacing="20" MaxWidth="800">
            <!-- Header -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{x:Bind ViewModel.IsEditMode, Converter={StaticResource BoolToEditTitleConverter}}" 
                          FontSize="28" FontWeight="Bold" VerticalAlignment="Center"/>
                <Button Grid.Column="1" Content="❌ Cancelar" Padding="15,10" Command="{x:Bind ViewModel.CancelEditCommand}"/>
            </Grid>

            <!-- Error Messages -->
            <Border Background="LightCoral" CornerRadius="5" Padding="15" Visibility="{x:Bind ViewModel.HasErrors, Mode=OneWay}">
                <TextBlock Text="{x:Bind ViewModel.ErrorMessage, Mode=OneWay}" Foreground="White" TextWrapping="Wrap"/>
            </Border>

            <!-- Form -->
            <Border Background="White" CornerRadius="8" Padding="30" BorderBrush="LightGray" BorderThickness="1">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Nombre -->
                    <StackPanel Grid.Column="0" Grid.Row="0" Spacing="5" Margin="0,0,15,20">
                        <TextBlock Text="Nombre *" FontWeight="SemiBold"/>
                        <TextBox Text="{x:Bind ViewModel.Nombre, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                PlaceholderText="Ingrese el nombre"/>
                    </StackPanel>

                    <!-- Apellidos -->
                    <StackPanel Grid.Column="1" Grid.Row="0" Spacing="5" Margin="15,0,0,20">
                        <TextBlock Text="Apellidos *" FontWeight="SemiBold"/>
                        <TextBox Text="{x:Bind ViewModel.Apellidos, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                PlaceholderText="Ingrese los apellidos"/>
                    </StackPanel>

                    <!-- Tipo de Documento -->
                    <StackPanel Grid.Column="0" Grid.Row="1" Spacing="5" Margin="0,0,15,20">
                        <TextBlock Text="Tipo de Documento *" FontWeight="SemiBold"/>
                        <ComboBox SelectedItem="{x:Bind ViewModel.TipoDocumento, Mode=TwoWay}" HorizontalAlignment="Stretch">
                            <models:TipoDocumento>Cedula</models:TipoDocumento>
                            <models:TipoDocumento>Pasaporte</models:TipoDocumento>
                            <models:TipoDocumento>RNC</models:TipoDocumento>
                            <models:TipoDocumento>Otro</models:TipoDocumento>
                        </ComboBox>
                    </StackPanel>

                    <!-- Documento de Identidad -->
                    <StackPanel Grid.Column="1" Grid.Row="1" Spacing="5" Margin="15,0,0,20">
                        <TextBlock Text="Documento de Identidad *" FontWeight="SemiBold"/>
                        <TextBox Text="{x:Bind ViewModel.DocumentoIdentidad, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                PlaceholderText="Ingrese el documento"/>
                    </StackPanel>

                    <!-- Email -->
                    <StackPanel Grid.Column="0" Grid.Row="2" Spacing="5" Margin="0,0,15,20">
                        <TextBlock Text="Email" FontWeight="SemiBold"/>
                        <TextBox Text="{x:Bind ViewModel.Email, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                PlaceholderText="<EMAIL>" InputScope="EmailNameOrAddress"/>
                    </StackPanel>

                    <!-- Teléfono -->
                    <StackPanel Grid.Column="1" Grid.Row="2" Spacing="5" Margin="15,0,0,20">
                        <TextBlock Text="Teléfono" FontWeight="SemiBold"/>
                        <TextBox Text="{x:Bind ViewModel.Telefono, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                PlaceholderText="(*************" InputScope="TelephoneNumber"/>
                    </StackPanel>

                    <!-- Dirección -->
                    <StackPanel Grid.Column="0" Grid.Row="3" Grid.ColumnSpan="2" Spacing="5" Margin="0,0,0,20">
                        <TextBlock Text="Dirección" FontWeight="SemiBold"/>
                        <TextBox Text="{x:Bind ViewModel.Direccion, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                PlaceholderText="Ingrese la dirección completa" AcceptsReturn="True" TextWrapping="Wrap" Height="80"/>
                    </StackPanel>

                    <!-- Estado Activo -->
                    <StackPanel Grid.Column="0" Grid.Row="4" Spacing="5" Margin="0,0,15,20">
                        <CheckBox Content="Cliente Activo" IsChecked="{x:Bind ViewModel.Activo, Mode=TwoWay}"/>
                    </StackPanel>

                    <!-- Botones de Acción -->
                    <StackPanel Grid.Column="0" Grid.Row="5" Grid.ColumnSpan="2" Orientation="Horizontal" 
                               HorizontalAlignment="Right" Spacing="10" Margin="0,20,0,0">
                        <Button Content="❌ Cancelar" Padding="20,10" Command="{x:Bind ViewModel.CancelEditCommand}"/>
                        <Button Content="💾 Guardar" Background="{ThemeResource SystemAccentColor}" 
                               Foreground="White" Padding="20,10" Command="{x:Bind ViewModel.SaveClienteCommand}"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Loading Indicator -->
            <StackPanel Visibility="{x:Bind ViewModel.IsBusy, Mode=OneWay}" HorizontalAlignment="Center" Spacing="10">
                <ProgressRing IsActive="{x:Bind ViewModel.IsBusy, Mode=OneWay}" Width="40" Height="40"/>
                <TextBlock Text="{x:Bind ViewModel.BusyText, Mode=OneWay}" HorizontalAlignment="Center"/>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</Page>
