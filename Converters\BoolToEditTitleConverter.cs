using Microsoft.UI.Xaml.Data;

namespace DinPresto.Converters
{
    public class BoolToEditTitleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is bool isEditMode)
            {
                return isEditMode ? "✏️ Editar Cliente" : "➕ Nuevo Cliente";
            }

            return "Cliente";
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
