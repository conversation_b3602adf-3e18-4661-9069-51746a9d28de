using Microsoft.UI.Xaml.Navigation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using DinPresto.Data;
using DinPresto.Services;
using DinPresto.ViewModels;
using DinPresto.Views;

namespace DinPresto
{
    /// <summary>
    /// Provides application-specific behavior to supplement the default Application class.
    /// </summary>
    public partial class App : Application
    {
        private Window? window;
        private IHost? host;

        /// <summary>
        /// Initializes the singleton application object.  This is the first line of authored code
        /// executed, and as such is the logical equivalent of main() or WinMain().
        /// </summary>
        public App()
        {
            this.InitializeComponent();

            // Configurar servicios
            host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Configurar Entity Framework
                    services.AddDbContext<DinPrestoContext>(options =>
                        options.UseSqlite("Data Source=dinpresto.db"));

                    // Registrar servicios
                    services.AddScoped<IAmortizacionService, AmortizacionService>();
                    services.AddScoped<IClienteService, ClienteService>();
                    services.AddSingleton<INavigationService, NavigationService>();

                    // Registrar ViewModels
                    services.AddTransient<MainViewModel>();
                    services.AddTransient<ClientesViewModel>();

                    // Registrar Pages
                    services.AddTransient<MainPage>();
                    services.AddTransient<DashboardPage>();
                    services.AddTransient<ClientesPage>();
                    services.AddTransient<PrestamosPage>();
                    services.AddTransient<PagosPage>();
                    services.AddTransient<ReportesPage>();
                    services.AddTransient<ConfiguracionPage>();
                })
                .Build();
        }

        /// <summary>
        /// Invoked when the application is launched normally by the end user.  Other entry points
        /// will be used such as when the application is launched to open a specific file.
        /// </summary>
        /// <param name="e">Details about the launch request and process.</param>
        protected override void OnLaunched(LaunchActivatedEventArgs e)
        {
            window = new Window();
            window.Title = "DinPresto - Sistema de Gestión de Préstamos";

            // Inicializar la base de datos
            InitializeDatabaseAsync();

            // Crear la página principal usando DI
            var mainPage = host?.Services.GetRequiredService<MainPage>();
            window.Content = mainPage;

            window.Activate();
        }

        private async void InitializeDatabaseAsync()
        {
            try
            {
                using var scope = host?.Services.CreateScope();
                var context = scope?.ServiceProvider.GetRequiredService<DinPrestoContext>();
                if (context != null)
                {
                    await context.Database.EnsureCreatedAsync();
                }
            }
            catch (Exception ex)
            {
                // Log error - en una implementación real usaríamos un logger
                System.Diagnostics.Debug.WriteLine($"Error inicializando base de datos: {ex.Message}");
            }
        }

        /// <summary>
        /// Invoked when Navigation to a certain page fails
        /// </summary>
        /// <param name="sender">The Frame which failed navigation</param>
        /// <param name="e">Details about the navigation failure</param>
        void OnNavigationFailed(object sender, NavigationFailedEventArgs e)
        {
            throw new Exception("Failed to load Page " + e.SourcePageType.FullName);
        }

        // El método OnClosed no existe en Application, se maneja en el Window
        // La limpieza se hará cuando la aplicación termine
    }
}
