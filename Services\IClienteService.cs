using DinPresto.Models;

namespace DinPresto.Services
{
    public interface IClienteService
    {
        Task<IEnumerable<Cliente>> GetAllClientesAsync();
        Task<IEnumerable<Cliente>> GetClientesActivosAsync();
        Task<Cliente?> GetClienteByIdAsync(int id);
        Task<Cliente?> GetClienteByDocumentoAsync(string documento);
        Task<Cliente> CreateClienteAsync(Cliente cliente);
        Task<Cliente> UpdateClienteAsync(Cliente cliente);
        Task<bool> DeleteClienteAsync(int id);
        Task<bool> CanDeleteClienteAsync(int id);
        Task<IEnumerable<Cliente>> SearchClientesAsync(string searchTerm);
        Task<IEnumerable<Cliente>> GetClientesByTipoDocumentoAsync(TipoDocumento tipoDocumento);
        Task<bool> ExisteDocumentoAsync(string documento, int? excludeClienteId = null);
        Task<decimal> GetTotalDeudaClienteAsync(int clienteId);
        Task<int> GetTotalPrestamosClienteAsync(int clienteId);
        Task<IEnumerable<Cliente>> GetClientesMorososAsync();
        Task<IEnumerable<Cliente>> GetMejoresClientesAsync(int top = 10);
    }
}
