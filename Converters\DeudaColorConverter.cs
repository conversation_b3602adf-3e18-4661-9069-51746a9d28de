using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI;

namespace DinPresto.Converters
{
    public class DeudaColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, string language)
        {
            if (value is decimal deuda)
            {
                if (deuda == 0)
                    return new SolidColorBrush(Colors.Green);
                else if (deuda > 50000)
                    return new SolidColorBrush(Colors.Red);
                else
                    return new SolidColorBrush(Colors.Orange);
            }

            return new SolidColorBrush(Colors.Black);
        }

        public object ConvertBack(object value, Type targetType, object parameter, string language)
        {
            throw new NotImplementedException();
        }
    }
}
