{"version": 3, "targets": {"net9.0-windows10.0.19041": {"CommunityToolkit.Mvvm/8.4.0": {"type": "package", "compile": {"lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "Humanizer.Core/2.14.1": {"type": "package", "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "compile": {"lib/netstandard2.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "compile": {"ref/net8.0/_._": {"related": ".xml"}}, "runtime": {"lib/net8.0/_._": {"related": ".pdb;.xml"}}}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {}}, "build": {"build/_._": {}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "build": {"buildTransitive/Microsoft.CodeAnalysis.Analyzers.props": {}, "buildTransitive/Microsoft.CodeAnalysis.Analyzers.targets": {}}}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "7.0.0", "System.Reflection.Metadata": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net7.0/_._": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.8.0]"}, "compile": {"lib/net7.0/_._": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "[4.8.0]", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]"}, "compile": {"lib/net7.0/_._": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "System.Composition": "7.0.0", "System.IO.Pipelines": "7.0.0", "System.Threading.Channels": "7.0.0"}, "compile": {"lib/net7.0/_._": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "dependencies": {"Microsoft.Build.Framework": "16.10.0", "Microsoft.CodeAnalysis.Common": "[4.8.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.8.0]", "System.Text.Json": "7.0.3"}, "compile": {"lib/net7.0/_._": {"related": ".pdb;.runtimeconfig.json;.xml"}}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"related": ".pdb;.runtimeconfig.json;.xml"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"related": ".BuildHost.pdb;.BuildHost.runtimeconfig.json;.BuildHost.xml;.pdb;.xml"}}, "resource": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Data.Sqlite.Core/9.0.9": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore/9.0.9": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.9", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.9": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.9": {"type": "package"}, "Microsoft.EntityFrameworkCore.Design/9.0.9": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyModel": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Mono.TextTemplating": "3.0.0", "System.Text.Json": "9.0.9"}, "compile": {"lib/net8.0/_._": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "build": {"build/net8.0/Microsoft.EntityFrameworkCore.Design.props": {}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.9": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.9": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyModel": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.9"}, "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.9": {"type": "package", "dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.9", "Microsoft.EntityFrameworkCore.Relational": "9.0.9", "Microsoft.Extensions.Caching.Memory": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyModel": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.9"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Tools/9.0.9": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Design": "9.0.9"}}, "Microsoft.Extensions.Caching.Abstractions/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Physical": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Configuration.Json": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Physical": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.9": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/9.0.9": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.9", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.FileSystemGlobbing": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.9": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Configuration.Binder": "9.0.9", "Microsoft.Extensions.Configuration.CommandLine": "9.0.9", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.9", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.9", "Microsoft.Extensions.Configuration.Json": "9.0.9", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.9", "Microsoft.Extensions.DependencyInjection": "9.0.9", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Diagnostics": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Physical": "9.0.9", "Microsoft.Extensions.Hosting.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Logging.Configuration": "9.0.9", "Microsoft.Extensions.Logging.Console": "9.0.9", "Microsoft.Extensions.Logging.Debug": "9.0.9", "Microsoft.Extensions.Logging.EventLog": "9.0.9", "Microsoft.Extensions.Logging.EventSource": "9.0.9", "Microsoft.Extensions.Options": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.9", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.9", "Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Configuration.Binder": "9.0.9", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Console/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Logging.Configuration": "9.0.9", "Microsoft.Extensions.Options": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Debug/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.EventLog/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9", "System.Diagnostics.EventLog": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.EventSource/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Logging": "9.0.9", "Microsoft.Extensions.Logging.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Options/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.9": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.9", "Microsoft.Extensions.Configuration.Binder": "9.0.9", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.9", "Microsoft.Extensions.Options": "9.0.9", "Microsoft.Extensions.Primitives": "9.0.9"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Primitives/9.0.9": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Web.WebView2/1.0.3485.44": {"type": "package", "build": {"buildTransitive/Microsoft.Web.WebView2.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/WebView2Loader.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.4948": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.Windows.SDK.BuildTools.MSIX/1.7.20250829.1": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.targets": {}}}, "Microsoft.WindowsAppSDK/1.8.250907003": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.AI": "[1.8.37]", "Microsoft.WindowsAppSDK.Base": "[1.8.250831001]", "Microsoft.WindowsAppSDK.DWrite": "[1.8.25090401]", "Microsoft.WindowsAppSDK.Foundation": "[1.8.250906002]", "Microsoft.WindowsAppSDK.InteractiveExperiences": "[1.8.250906004]", "Microsoft.WindowsAppSDK.Runtime": "[1.8.250907003]", "Microsoft.WindowsAppSDK.Widgets": "[1.8.250904007]", "Microsoft.WindowsAppSDK.WinUI": "[1.8.250906003]"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}}, "Microsoft.WindowsAppSDK.AI/1.8.37": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.AI.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.AI.targets": {}}}, "Microsoft.WindowsAppSDK.Base/1.8.250831001": {"type": "package", "dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.26100.4654", "Microsoft.Windows.SDK.BuildTools.MSIX": "1.7.20250829.1"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Base.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Base.targets": {}}}, "Microsoft.WindowsAppSDK.DWrite/1.8.25090401": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.DWrite.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.DWrite.targets": {}}}, "Microsoft.WindowsAppSDK.Foundation/1.8.250906002": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll": {}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Foundation.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Foundation.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64ec/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-arm64ec"}, "runtimes/win-arm64ec/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-arm64ec"}, "runtimes/win-x64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.WindowsAppSDK.InteractiveExperiences/1.8.250906004": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "compile": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.targets": {}}}, "Microsoft.WindowsAppSDK.Runtime/1.8.250907003": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Runtime.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Runtime.targets": {}}}, "Microsoft.WindowsAppSDK.Widgets/1.8.250904007": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK.Base": "1.8.250831001"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.Widgets.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.Widgets.targets": {}}}, "Microsoft.WindowsAppSDK.WinUI/1.8.250906003": {"type": "package", "dependencies": {"Microsoft.Web.WebView2": "1.0.3179.45", "Microsoft.WindowsAppSDK.Base": "1.8.250831001", "Microsoft.WindowsAppSDK.Foundation": "1.8.250906002", "Microsoft.WindowsAppSDK.InteractiveExperiences": "1.8.250906004"}, "compile": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll": {"related": ".xml"}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.WinUI.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.WinUI.targets": {}}}, "Mono.TextTemplating/3.0.0": {"type": "package", "dependencies": {"System.CodeDom": "6.0.0"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {}}, "build": {"buildTransitive/Mono.TextTemplating.targets": {}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "build": {"buildTransitive/net9.0/SQLitePCLRaw.lib.e_sqlite3.targets": {}}, "runtimeTargets": {"runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a": {"assetType": "native", "rid": "browser-wasm"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-armel"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-mips64"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-s390x"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-ppc64le"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-s390x"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x86"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x86"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}}, "System.CodeDom/6.0.0": {"type": "package", "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Collections.Immutable/7.0.0": {"type": "package", "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition/7.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}, "compile": {"lib/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Convention/7.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Hosting/7.0.0": {"type": "package", "dependencies": {"System.Composition.Runtime": "7.0.0"}, "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.Runtime/7.0.0": {"type": "package", "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Composition.TypedParts/7.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/9.0.9": {"type": "package", "compile": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Pipelines/7.0.0": {"type": "package", "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Reflection.Metadata/7.0.0": {"type": "package", "dependencies": {"System.Collections.Immutable": "7.0.0"}, "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/_._": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Json/9.0.9": {"type": "package", "compile": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "System.Threading.Channels/7.0.0": {"type": "package", "compile": {"lib/net7.0/_._": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Threading.Channels.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}}}, "libraries": {"CommunityToolkit.Mvvm/8.4.0": {"sha512": "tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "type": "package", "path": "communitytoolkit.mvvm/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.Windows.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.Windows.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.4.0.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "Humanizer.Core/2.14.1": {"sha512": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "type": "package", "path": "humanizer.core/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.2.14.1.nupkg.sha512", "humanizer.core.nuspec", "lib/net6.0/Humanizer.dll", "lib/net6.0/Humanizer.xml", "lib/netstandard1.0/Humanizer.dll", "lib/netstandard1.0/Humanizer.xml", "lib/netstandard2.0/Humanizer.dll", "lib/netstandard2.0/Humanizer.xml", "logo.png"]}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"sha512": "3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Build.Framework/17.8.3": {"sha512": "NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "type": "package", "path": "microsoft.build.framework/17.8.3", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.Build.Framework.dll", "lib/net472/Microsoft.Build.Framework.pdb", "lib/net472/Microsoft.Build.Framework.xml", "lib/net8.0/Microsoft.Build.Framework.dll", "lib/net8.0/Microsoft.Build.Framework.pdb", "lib/net8.0/Microsoft.Build.Framework.xml", "microsoft.build.framework.17.8.3.nupkg.sha512", "microsoft.build.framework.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.Build.Framework.dll", "ref/net472/Microsoft.Build.Framework.xml", "ref/net8.0/Microsoft.Build.Framework.dll", "ref/net8.0/Microsoft.Build.Framework.xml", "ref/netstandard2.0/Microsoft.Build.Framework.dll", "ref/netstandard2.0/Microsoft.Build.Framework.xml"]}, "Microsoft.Build.Locator/1.7.8": {"sha512": "sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "type": "package", "path": "microsoft.build.locator/1.7.8", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "build/Microsoft.Build.Locator.props", "build/Microsoft.Build.Locator.targets", "lib/net46/Microsoft.Build.Locator.dll", "lib/net6.0/Microsoft.Build.Locator.dll", "microsoft.build.locator.1.7.8.nupkg.sha512", "microsoft.build.locator.nuspec"]}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"sha512": "AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "type": "package", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.txt", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "buildTransitive/Microsoft.CodeAnalysis.Analyzers.props", "buildTransitive/Microsoft.CodeAnalysis.Analyzers.targets", "buildTransitive/config/analysislevel_2_9_8_all.globalconfig", "buildTransitive/config/analysislevel_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_default.globalconfig", "buildTransitive/config/analysislevel_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevel_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_none.globalconfig", "buildTransitive/config/analysislevel_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevel_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_all.globalconfig", "buildTransitive/config/analysislevel_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_default.globalconfig", "buildTransitive/config/analysislevel_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_none.globalconfig", "buildTransitive/config/analysislevel_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_all.globalconfig", "buildTransitive/config/analysislevel_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_default.globalconfig", "buildTransitive/config/analysislevel_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_none.globalconfig", "buildTransitive/config/analysislevel_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_all.globalconfig", "buildTransitive/config/analysislevel_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_default.globalconfig", "buildTransitive/config/analysislevel_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_minimum.globalconfig", "buildTransitive/config/analysislevel_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_none.globalconfig", "buildTransitive/config/analysislevel_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_3_recommended.globalconfig", "buildTransitive/config/analysislevel_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_all.globalconfig", "buildTransitive/config/analysislevel_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_default.globalconfig", "buildTransitive/config/analysislevel_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_minimum.globalconfig", "buildTransitive/config/analysislevel_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_none.globalconfig", "buildTransitive/config/analysislevel_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevel_4_3_recommended.globalconfig", "buildTransitive/config/analysislevel_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_all.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_default.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_none.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelcorrectness_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_all.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_default.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_none.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_all.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_default.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_minimum.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_none.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_recommended.globalconfig", "buildTransitive/config/analysislevellibrary_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscompatibility_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysiscorrectness_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdesign_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisdocumentation_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysislocalization_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisperformance_4_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_recommended_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_all.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_all_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_default.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_default_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_minimum.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_minimum_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_none.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_none_warnaserror.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_recommended.globalconfig", "buildTransitive/config/analysislevelmicrosoftcodeanalysisreleasetracking_4_3_recommended_warnaserror.globalconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.Analyzers.md", "documentation/Microsoft.CodeAnalysis.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/CorrectnessRulesDefault/.editorconfig", "editorconfig/CorrectnessRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/LibraryRulesDefault/.editorconfig", "editorconfig/LibraryRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "microsoft.codeanalysis.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/CorrectnessRulesDefault.ruleset", "rulesets/CorrectnessRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/LibraryRulesDefault.ruleset", "rulesets/LibraryRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.Common/4.8.0": {"sha512": "/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "type": "package", "path": "microsoft.codeanalysis.common/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.dll", "lib/net6.0/Microsoft.CodeAnalysis.pdb", "lib/net6.0/Microsoft.CodeAnalysis.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.dll", "lib/net7.0/Microsoft.CodeAnalysis.pdb", "lib/net7.0/Microsoft.CodeAnalysis.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "microsoft.codeanalysis.common.4.8.0.nupkg.sha512", "microsoft.codeanalysis.common.nuspec"]}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"sha512": "+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "type": "package", "path": "microsoft.codeanalysis.csharp/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512", "microsoft.codeanalysis.csharp.nuspec"]}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"sha512": "3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "type": "package", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/net6.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512", "microsoft.codeanalysis.csharp.workspaces.nuspec"]}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"sha512": "LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "type": "package", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512", "microsoft.codeanalysis.workspaces.common.nuspec"]}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"sha512": "IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "type": "package", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.pdb", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.xml", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.pdb", "lib/net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.xml", "lib/net472/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net472/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.pdb", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.xml", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.pdb", "lib/net6.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.xml", "lib/net6.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net6.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.pdb", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.xml", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.pdb", "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.xml", "lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll", "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512", "microsoft.codeanalysis.workspaces.msbuild.nuspec"]}, "Microsoft.Data.Sqlite.Core/9.0.9": {"sha512": "DjxZRueHp0qvZxhvW+H1IWYkSofZI8Chg710KYJjNP/6S4q3rt97pvR8AHOompkSwaN92VLKz5uw01iUt85cMg==", "type": "package", "path": "microsoft.data.sqlite.core/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Data.Sqlite.dll", "lib/net6.0/Microsoft.Data.Sqlite.xml", "lib/net8.0/Microsoft.Data.Sqlite.dll", "lib/net8.0/Microsoft.Data.Sqlite.xml", "lib/netstandard2.0/Microsoft.Data.Sqlite.dll", "lib/netstandard2.0/Microsoft.Data.Sqlite.xml", "microsoft.data.sqlite.core.9.0.9.nupkg.sha512", "microsoft.data.sqlite.core.nuspec"]}, "Microsoft.EntityFrameworkCore/9.0.9": {"sha512": "zkt5yQgnpWKX3rOxn+ZcV23Aj0296XCTqg4lx1hKY+wMXBgkn377UhBrY/A4H6kLpNT7wqZN98xCV0YHXu9VRA==", "type": "package", "path": "microsoft.entityframeworkcore/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.9.0.9.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.9": {"sha512": "QdM2k3Mnip2QsaxJbCI95dc2SajRMENdmaMhVKj4jPC5dmkoRcu3eEdvZAgDbd4bFVV1jtPGdHtXewtoBMlZqA==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.9.0.9.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.9": {"sha512": "uiKeU/qR0YpaDUa4+g0rAjKCuwfq8YWZGcpPptnFWIr1K7dXQTm/15D2HDwwU4ln3Uf66krYybymuY58ua4hhw==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "docs/PACKAGE.md", "microsoft.entityframeworkcore.analyzers.9.0.9.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Design/9.0.9": {"sha512": "cFxH70tohWe3ugCjLhZB01mR7WHpg5dEK6zHsbkDFfpLxWT+HoZQKgchTJgF4bPWBPTyrlYlqfPY212fFtmJjg==", "type": "package", "path": "microsoft.entityframeworkcore.design/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net8.0/Microsoft.EntityFrameworkCore.Design.props", "lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Design.xml", "microsoft.entityframeworkcore.design.9.0.9.nupkg.sha512", "microsoft.entityframeworkcore.design.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/9.0.9": {"sha512": "SonFU9a8x4jZIhIBtCw1hIE3QKjd4c7Y3mjptoh682dfQe7K9pUPGcEV/sk4n8AJdq4fkyJPCaOdYaObhae/Iw==", "type": "package", "path": "microsoft.entityframeworkcore.relational/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.9.0.9.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.9": {"sha512": "SiAd32IMTAQDo+jQt5GAzCq+5qI/OEdsrbW0qEDr0hUEAh3jnRlt0gbZgDGDUtWk5SWITufB6AOZi0qet9dJIw==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/_._", "microsoft.entityframeworkcore.sqlite.9.0.9.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.9": {"sha512": "eQVF8fBgDxjnjan3EB1ysdfDO7lKKfWKTT4VR0BInU4Mi6ADdgiOdm6qvZ/ufh04f3hhPL5lyknx5XotGzBh8A==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.xml", "microsoft.entityframeworkcore.sqlite.core.9.0.9.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.core.nuspec"]}, "Microsoft.EntityFrameworkCore.Tools/9.0.9": {"sha512": "Q8n1PXXJApa1qX8HI3r/YuHoJ1HuLwjI2hLqaCV9K9pqQhGpi6Z38laOYwL2ElUOTWCxTKMDEMMYWfPlw6rwgg==", "type": "package", "path": "microsoft.entityframeworkcore.tools/9.0.9", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "docs/PACKAGE.md", "microsoft.entityframeworkcore.tools.9.0.9.nupkg.sha512", "microsoft.entityframeworkcore.tools.nuspec", "tools/EntityFrameworkCore.PS2.psd1", "tools/EntityFrameworkCore.PS2.psm1", "tools/EntityFrameworkCore.psd1", "tools/EntityFrameworkCore.psm1", "tools/about_EntityFrameworkCore.help.txt", "tools/init.ps1", "tools/net472/any/ef.exe", "tools/net472/win-arm64/ef.exe", "tools/net472/win-x86/ef.exe", "tools/netcoreapp2.0/any/ef.dll", "tools/netcoreapp2.0/any/ef.runtimeconfig.json"]}, "Microsoft.Extensions.Caching.Abstractions/9.0.9": {"sha512": "NgtRHOdPrAEacfjXLSrH/SRrSqGf6Vaa6d16mW2yoyJdg7AJr0BnBvxkv7PkCm/CHVyzojTK7Y+oUDEulqY1Qw==", "type": "package", "path": "microsoft.extensions.caching.abstractions/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.9.0.9.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/9.0.9": {"sha512": "ln31BtsDsBQxykJgxuCtiUXWRET9FmqeEq0BpPIghkYtGpDDVs8ZcLHAjCCzbw6aGoLek4Z7JaDjSO/CjOD0iw==", "type": "package", "path": "microsoft.extensions.caching.memory/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net9.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net9.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.9.0.9.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/9.0.9": {"sha512": "w87wF/90/VI0ZQBhf4rbMEeyEy0vi2WKjFmACsNAKNaorY+ZlVz7ddyXkbADvaWouMKffNmR0yQOGcrvSSvKGg==", "type": "package", "path": "microsoft.extensions.configuration/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.9.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.9": {"sha512": "p5RKAY9POvs3axwA/AQRuJeM8AHuE8h4qbP1NxQeGm0ep46aXz1oCLAp/oOYxX1GsjStgdhHrN3XXLLXr0+b3w==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.9.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.9": {"sha512": "6SIp/6Bngk4jm2W36JekZbiIbFPdE/eMUtrJEqIqHGpd1zar3jvgnwxnpWQfzUiGrkyY8q8s6V82zkkEZozghA==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.9.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/9.0.9": {"sha512": "9bzGOcHoTi8ijrj0MHh5qUY6n9CuittZUqEOj5iE0ZJoSCfG0BI9nhcpd8MC9bOOgjZW5OeizKO8rgta9lSVyA==", "type": "package", "path": "microsoft.extensions.configuration.commandline/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.9.0.9.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.9": {"sha512": "AB8suTh4STAMGDkPer5vL0YNp09eplvbkIbOfFJ1z8D1zOiFF8Hipk9FhCLU4Ea6TosWmGrK30ZIUO9KvAeFcg==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.9.0.9.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.9": {"sha512": "fvgubCs++wTowHWuQ5TAyZV0S6ldA59U+tBVqFr4/WLd0oEf6ESbdBN2CFaVdn4sZqnarqMnl2O3++RG/Jrf/w==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.9.0.9.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/9.0.9": {"sha512": "PiPYo1GTinR2ECM80zYdZUIFmde6jj5DryXUcOJg3yIjh+KQMQr42e+COD03QUsUiqNkJk511wVTnVpTm2AVZA==", "type": "package", "path": "microsoft.extensions.configuration.json/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.9.0.9.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.9": {"sha512": "bFaNxfU8gQJX3K/Dd6XT0YIJ5ZVihdAY6Z02p2nVTUHjUsaWflLIucZOgB/ecSNnN3zbbBEf1oFC7q5NHTZIHw==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.9.0.9.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.9": {"sha512": "zQV2WOSP+3z1EuK91ULxfGgo2Y75bTRnmJHp08+w/YXAyekZutX/qCd88/HOMNh35MDW9mJJJxPpMPS+1Rww8A==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.9.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.9": {"sha512": "/hymojfWbE9AlDOa0mczR44m00Jj+T3+HZO0ZnVTI032fVycI0ZbNOVFP6kqZMcXiLSYXzR2ilcwaRi6dzeGyA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.9.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/9.0.9": {"sha512": "fNGvKct2De8ghm0Bpfq0iWthtzIWabgOTi+gJhNOPhNJIowXNEUE2eZNW/zNCzrHVA3PXg2yZ+3cWZndC2IqYA==", "type": "package", "path": "microsoft.extensions.dependencymodel/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/net9.0/Microsoft.Extensions.DependencyModel.dll", "lib/net9.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.9.0.9.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics/9.0.9": {"sha512": "gtzl9SD6CvFYOb92qEF41Z9rICzYniM342TWbbJwN3eLS6a5fCLFvO1pQGtpMSnP3h1zHXupMEeKSA9musWYCQ==", "type": "package", "path": "microsoft.extensions.diagnostics/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.9.0.9.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.9": {"sha512": "YHGmxccrVZ2Ar3eI+/NdbOHkd1/HzrHvmQ5yBsp0Gl7jTyBe6qcXNYjUt9v9JIO+Z14la44+YYEe63JSqs1fYg==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.9.0.9.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.9": {"sha512": "M1ZhL9QkBQ/k6l/Wjgcli5zrV86HzytQ+gQiNtk9vs9Ge1fb17KKZil9T6jd15p2x/BGfXpup7Hg55CC0kkfig==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.9.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/9.0.9": {"sha512": "sRrPtEwbK23OCFOQ36Xn6ofiB0/nl54/BOdR7lJ/Vwg3XlyvUdmyXvFUS1EU5ltn+sQtbcPuy1l0hsysO8++SQ==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.9.0.9.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/9.0.9": {"sha512": "iQAgORaVIlkhcpxFnVEfjqNWfQCwBEEH7x2IanTwGafA6Tb4xiBoDWySTxUo3MV2NUV/PmwS/8OhT/elPnJCnw==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.9.0.9.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/9.0.9": {"sha512": "DmRsWH3g8yZGho/pLQ79hxhM2ctE1eDTZ/HbAnrD/uw8m+P2pRRJOoBVxlrhbhMP3/y3oAJoy0yITasfmilbTg==", "type": "package", "path": "microsoft.extensions.hosting/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "lib/net462/Microsoft.Extensions.Hosting.dll", "lib/net462/Microsoft.Extensions.Hosting.xml", "lib/net8.0/Microsoft.Extensions.Hosting.dll", "lib/net8.0/Microsoft.Extensions.Hosting.xml", "lib/net9.0/Microsoft.Extensions.Hosting.dll", "lib/net9.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.9.0.9.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/9.0.9": {"sha512": "ORA4dICNz7cuwupPkjXpSuoiK6GMg0aygInBIQCCFEimwoHntRKdJqB59faxq2HHJuTPW3NsZm5EjN5P5Zh6nQ==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.9.0.9.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.9": {"sha512": "MaCB0Y9hNDs4YLu3HCJbo199WnJT8xSgajG1JYGANz9FkseQ5f3v/llu3HxLI6mjDlu7pa7ps9BLPWjKzsAAzQ==", "type": "package", "path": "microsoft.extensions.logging/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.9.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.9": {"sha512": "FEgpSF+Z9StMvrsSViaybOBwR0f0ZZxDm8xV5cSOFiXN/t+ys+rwAlTd/6yG7Ld1gfppgvLcMasZry3GsI9lGA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.9.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/9.0.9": {"sha512": "Abuo+S0Sg+Ke6vzSh5Ell+lwJJM+CEIqg1ImtWnnqF6a/ibJkQnmFJi4/ekEw/0uAcdFKJXtGV7w6cFN0nyXeg==", "type": "package", "path": "microsoft.extensions.logging.configuration/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.9.0.9.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/9.0.9": {"sha512": "x3+W7IfW9Tg3sV+sU9N1039M4CqklaAecwhz9qNtjOCBdmg7h96JaL+NAvhYgZgweVJTJaxAvuO8I+ZZehE7Pg==", "type": "package", "path": "microsoft.extensions.logging.console/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "lib/net462/Microsoft.Extensions.Logging.Console.dll", "lib/net462/Microsoft.Extensions.Logging.Console.xml", "lib/net8.0/Microsoft.Extensions.Logging.Console.dll", "lib/net8.0/Microsoft.Extensions.Logging.Console.xml", "lib/net9.0/Microsoft.Extensions.Logging.Console.dll", "lib/net9.0/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.9.0.9.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/9.0.9": {"sha512": "q8IbjIzTjfaGfuf9LAuG3X9BytAWj2hWhLU61rEkit847oaSSbcdx/yybY3yL9RgVG1u9ctk7kbCv18M+7Fi6Q==", "type": "package", "path": "microsoft.extensions.logging.debug/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net9.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net9.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.9.0.9.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/9.0.9": {"sha512": "1SX5+mv16SBb5NrtLNxIvUt8PHbdvDloZazQdxz1CNM39jG7yeF6olH3sceQ4ONF0oVD5mVUsTag0iVX4xgyog==", "type": "package", "path": "microsoft.extensions.logging.eventlog/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets", "lib/net462/Microsoft.Extensions.Logging.EventLog.dll", "lib/net462/Microsoft.Extensions.Logging.EventLog.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.9.0.9.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/9.0.9": {"sha512": "rGQi5mImot7tTFxj1tQWknWjOBHX1+gsX1WLmQNl5WHr4Sx1kXUBGDuRUjfx4c8pe/hcYHdalAmgk7RdusW6Jw==", "type": "package", "path": "microsoft.extensions.logging.eventsource/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "lib/net462/Microsoft.Extensions.Logging.EventSource.dll", "lib/net462/Microsoft.Extensions.Logging.EventSource.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.9.0.9.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.9": {"sha512": "loxGGHE1FC2AefwPHzrjPq7X92LQm64qnU/whKfo6oWaceewPUVYQJBJs3S3E2qlWwnCpeZ+dGCPTX+5dgVAuQ==", "type": "package", "path": "microsoft.extensions.options/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.9.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.9": {"sha512": "n4DCdnn2qs6V5U06Sx62FySEAZsJiJJgOzrPHDh9hPK7c2W8hEabC76F3Re3tGPjpiKa02RvB6FxZyxo8iICzg==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.9.0.9.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.9": {"sha512": "z4pyMePOrl733ltTowbN565PxBw1oAr8IHmIXNDiDqd22nFpYltX9KhrNC/qBWAG1/Zx5MHX+cOYhWJQYCO/iw==", "type": "package", "path": "microsoft.extensions.primitives/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.9.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Web.WebView2/1.0.3485.44": {"sha512": "4hk+MDJcW6Hcvtfb0qv0zba9mpBFxM2GO9gJ+8Nq/zoAdwFanmt7jzvM07NPB/UcuQ/jZpKh9IUDL/eVOEFh2A==", "type": "package", "path": "microsoft.web.webview2/1.0.3485.44", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "NOTICE.txt", "WebView2.idl", "WebView2.tlb", "build/Common.targets", "build/Microsoft.Web.WebView2.targets", "build/WebView2Rules.Project.xml", "build/native/Microsoft.Web.WebView2.targets", "build/native/arm64/WebView2Loader.dll", "build/native/arm64/WebView2Loader.dll.lib", "build/native/arm64/WebView2LoaderStatic.lib", "build/native/include-winrt/WebView2Interop.h", "build/native/include-winrt/WebView2Interop.idl", "build/native/include-winrt/WebView2Interop.tlb", "build/native/include/WebView2.h", "build/native/include/WebView2EnvironmentOptions.h", "build/native/x64/WebView2Loader.dll", "build/native/x64/WebView2Loader.dll.lib", "build/native/x64/WebView2LoaderStatic.lib", "build/native/x86/WebView2Loader.dll", "build/native/x86/WebView2Loader.dll.lib", "build/native/x86/WebView2LoaderStatic.lib", "build/wv2winrt.targets", "buildTransitive/Microsoft.Web.WebView2.targets", "lib/Microsoft.Web.WebView2.Core.winmd", "lib/net462/Microsoft.Web.WebView2.Core.dll", "lib/net462/Microsoft.Web.WebView2.Core.xml", "lib/net462/Microsoft.Web.WebView2.WinForms.dll", "lib/net462/Microsoft.Web.WebView2.WinForms.xml", "lib/net462/Microsoft.Web.WebView2.Wpf.dll", "lib/net462/Microsoft.Web.WebView2.Wpf.xml", "lib_manual/net5.0-windows10.0.17763.0/Microsoft.Web.WebView2.Wpf.dll", "lib_manual/net5.0-windows10.0.17763.0/Microsoft.Web.WebView2.Wpf.xml", "lib_manual/net6.0-windows10.0.17763.0/Microsoft.Web.WebView2.Core.Projection.dll", "lib_manual/net8.0-windows10.0.17763.0/Microsoft.Web.WebView2.Core.Projection.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Core.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Core.xml", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.WinForms.xml", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.dll", "lib_manual/netcoreapp3.0/Microsoft.Web.WebView2.Wpf.xml", "microsoft.web.webview2.1.0.3485.44.nupkg.sha512", "microsoft.web.webview2.nuspec", "runtimes/win-arm64/native/WebView2Loader.dll", "runtimes/win-arm64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x64/native/WebView2Loader.dll", "runtimes/win-x64/native_uap/Microsoft.Web.WebView2.Core.dll", "runtimes/win-x86/native/WebView2Loader.dll", "runtimes/win-x86/native_uap/Microsoft.Web.WebView2.Core.dll", "tools/VisualStudioToolsManifest.xml", "tools/wv2winrt/Antlr3.Runtime.dll", "tools/wv2winrt/Antlr4.StringTemplate.dll", "tools/wv2winrt/System.Buffers.dll", "tools/wv2winrt/System.CommandLine.DragonFruit.dll", "tools/wv2winrt/System.CommandLine.Rendering.dll", "tools/wv2winrt/System.CommandLine.dll", "tools/wv2winrt/System.Memory.dll", "tools/wv2winrt/System.Numerics.Vectors.dll", "tools/wv2winrt/System.Runtime.CompilerServices.Unsafe.dll", "tools/wv2winrt/codegen_util.dll", "tools/wv2winrt/concrt140_app.dll", "tools/wv2winrt/cs/System.CommandLine.resources.dll", "tools/wv2winrt/de/System.CommandLine.resources.dll", "tools/wv2winrt/es/System.CommandLine.resources.dll", "tools/wv2winrt/fr/System.CommandLine.resources.dll", "tools/wv2winrt/it/System.CommandLine.resources.dll", "tools/wv2winrt/ja/System.CommandLine.resources.dll", "tools/wv2winrt/ko/System.CommandLine.resources.dll", "tools/wv2winrt/msvcp140_1_app.dll", "tools/wv2winrt/msvcp140_2_app.dll", "tools/wv2winrt/msvcp140_app.dll", "tools/wv2winrt/pl/System.CommandLine.resources.dll", "tools/wv2winrt/pt-BR/System.CommandLine.resources.dll", "tools/wv2winrt/ru/System.CommandLine.resources.dll", "tools/wv2winrt/tr/System.CommandLine.resources.dll", "tools/wv2winrt/type_hierarchy.dll", "tools/wv2winrt/vcamp140_app.dll", "tools/wv2winrt/vccorlib140_app.dll", "tools/wv2winrt/vcomp140_app.dll", "tools/wv2winrt/vcruntime140_app.dll", "tools/wv2winrt/winrt_winmd.dll", "tools/wv2winrt/winrt_winmd.winmd", "tools/wv2winrt/wv2winrt.exe", "tools/wv2winrt/wv2winrt.exe.config", "tools/wv2winrt/wv2winrt.xml", "tools/wv2winrt/zh-Hans/System.CommandLine.resources.dll", "tools/wv2winrt/zh-Hant/System.CommandLine.resources.dll"]}, "Microsoft.Windows.SDK.BuildTools/10.0.26100.4948": {"sha512": "o0T4CVaumDjPNNijKiM7p25vHKdyKqYvaVVLgQO02KTOoUDlgMYJVUQAXn1IG0G9/ZsdZ+bdgWxgQsrO/b37qw==", "type": "package", "path": "microsoft.windows.sdk.buildtools/10.0.26100.4948", "files": [".nupkg.metadata", ".signature.p7s", "bin/10.0.26100.0/arm64/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/arm64/ComparePackage.exe", "bin/10.0.26100.0/arm64/DeployUtil.exe", "bin/10.0.26100.0/arm64/MakeCert.exe", "bin/10.0.26100.0/arm64/Microsoft.ComparePackage.Lib.dll", "bin/10.0.26100.0/arm64/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/arm64/Microsoft.PackageEditor.Lib.dll", "bin/10.0.26100.0/arm64/Microsoft.Tools.Connectivity.dll", "bin/10.0.26100.0/arm64/Microsoft.Tools.Deploy.dll", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.26100.0/arm64/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.26100.0/arm64/PackageEditor.exe", "bin/10.0.26100.0/arm64/ServicingCommon.dll", "bin/10.0.26100.0/arm64/SirepClient.assembly.manifest", "bin/10.0.26100.0/arm64/SirepClient.dll", "bin/10.0.26100.0/arm64/SirepInterop.dll", "bin/10.0.26100.0/arm64/SshClient.dll", "bin/10.0.26100.0/arm64/WinAppDeployCmd.exe", "bin/10.0.26100.0/arm64/WinAppDeployCommon.dll", "bin/10.0.26100.0/arm64/appxpackaging.dll", "bin/10.0.26100.0/arm64/appxsip.dll", "bin/10.0.26100.0/arm64/en-US/AppxPackaging.dll.mui", "bin/10.0.26100.0/arm64/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.26100.0/arm64/en/WinAppDeployCmd.resources.dll", "bin/10.0.26100.0/arm64/ipoverusb.discoverpartners.dll", "bin/10.0.26100.0/arm64/makeappx.exe", "bin/10.0.26100.0/arm64/makecat.exe", "bin/10.0.26100.0/arm64/makecat.exe.manifest", "bin/10.0.26100.0/arm64/makepri.exe", "bin/10.0.26100.0/arm64/mc.exe", "bin/10.0.26100.0/arm64/mdmerge.exe", "bin/10.0.26100.0/arm64/midl.exe", "bin/10.0.26100.0/arm64/midlc.exe", "bin/10.0.26100.0/arm64/midlrt.exe", "bin/10.0.26100.0/arm64/midlrtmd.dll", "bin/10.0.26100.0/arm64/mrmsupport.dll", "bin/10.0.26100.0/arm64/msisip.dll", "bin/10.0.26100.0/arm64/mssign32.dll", "bin/10.0.26100.0/arm64/mt.exe", "bin/10.0.26100.0/arm64/mt.exe.config", "bin/10.0.26100.0/arm64/opcservices.dll", "bin/10.0.26100.0/arm64/rc.exe", "bin/10.0.26100.0/arm64/rcdll.dll", "bin/10.0.26100.0/arm64/signtool.exe", "bin/10.0.26100.0/arm64/signtool.exe.manifest", "bin/10.0.26100.0/arm64/tracewpp.exe", "bin/10.0.26100.0/arm64/uuidgen.exe", "bin/10.0.26100.0/arm64/veiid.exe", "bin/10.0.26100.0/arm64/winmdidl.exe", "bin/10.0.26100.0/arm64/wintrust.dll", "bin/10.0.26100.0/arm64/wintrust.dll.ini", "bin/10.0.26100.0/x64/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x64/ComparePackage.exe", "bin/10.0.26100.0/x64/DeployUtil.exe", "bin/10.0.26100.0/x64/MakeCert.exe", "bin/10.0.26100.0/x64/Microsoft.ComparePackage.Lib.dll", "bin/10.0.26100.0/x64/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x64/Microsoft.PackageEditor.Lib.dll", "bin/10.0.26100.0/x64/Microsoft.Tools.Connectivity.dll", "bin/10.0.26100.0/x64/Microsoft.Tools.Deploy.dll", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.26100.0/x64/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.26100.0/x64/PackageEditor.exe", "bin/10.0.26100.0/x64/ServicingCommon.dll", "bin/10.0.26100.0/x64/SirepClient.assembly.manifest", "bin/10.0.26100.0/x64/SirepClient.dll", "bin/10.0.26100.0/x64/SirepInterop.dll", "bin/10.0.26100.0/x64/SshClient.dll", "bin/10.0.26100.0/x64/WinAppDeployCmd.exe", "bin/10.0.26100.0/x64/WinAppDeployCommon.dll", "bin/10.0.26100.0/x64/appxpackaging.dll", "bin/10.0.26100.0/x64/appxsip.dll", "bin/10.0.26100.0/x64/en-US/AppxPackaging.dll.mui", "bin/10.0.26100.0/x64/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.26100.0/x64/en/WinAppDeployCmd.resources.dll", "bin/10.0.26100.0/x64/ipoverusb.discoverpartners.dll", "bin/10.0.26100.0/x64/makeappx.exe", "bin/10.0.26100.0/x64/makecat.exe", "bin/10.0.26100.0/x64/makecat.exe.manifest", "bin/10.0.26100.0/x64/makepri.exe", "bin/10.0.26100.0/x64/mc.exe", "bin/10.0.26100.0/x64/mdmerge.exe", "bin/10.0.26100.0/x64/midl.exe", "bin/10.0.26100.0/x64/midlc.exe", "bin/10.0.26100.0/x64/midlrt.exe", "bin/10.0.26100.0/x64/midlrtmd.dll", "bin/10.0.26100.0/x64/mrmsupport.dll", "bin/10.0.26100.0/x64/msisip.dll", "bin/10.0.26100.0/x64/mssign32.dll", "bin/10.0.26100.0/x64/mt.exe", "bin/10.0.26100.0/x64/mt.exe.config", "bin/10.0.26100.0/x64/opcservices.dll", "bin/10.0.26100.0/x64/rc.exe", "bin/10.0.26100.0/x64/rcdll.dll", "bin/10.0.26100.0/x64/signtool.exe", "bin/10.0.26100.0/x64/signtool.exe.manifest", "bin/10.0.26100.0/x64/tracewpp.exe", "bin/10.0.26100.0/x64/uuidgen.exe", "bin/10.0.26100.0/x64/veiid.exe", "bin/10.0.26100.0/x64/winmdidl.exe", "bin/10.0.26100.0/x64/wintrust.dll", "bin/10.0.26100.0/x64/wintrust.dll.ini", "bin/10.0.26100.0/x86/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x86/ComparePackage.exe", "bin/10.0.26100.0/x86/DeployUtil.exe", "bin/10.0.26100.0/x86/MakeCert.exe", "bin/10.0.26100.0/x86/Microsoft.ComparePackage.Lib.dll", "bin/10.0.26100.0/x86/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.26100.0/x86/Microsoft.PackageEditor.Lib.dll", "bin/10.0.26100.0/x86/Microsoft.Tools.Connectivity.dll", "bin/10.0.26100.0/x86/Microsoft.Tools.Deploy.dll", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.26100.0/x86/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.26100.0/x86/PackageEditor.exe", "bin/10.0.26100.0/x86/ServicingCommon.dll", "bin/10.0.26100.0/x86/SirepClient.assembly.manifest", "bin/10.0.26100.0/x86/SirepClient.dll", "bin/10.0.26100.0/x86/SirepInterop.dll", "bin/10.0.26100.0/x86/SshClient.dll", "bin/10.0.26100.0/x86/WinAppDeployCmd.exe", "bin/10.0.26100.0/x86/WinAppDeployCommon.dll", "bin/10.0.26100.0/x86/appxpackaging.dll", "bin/10.0.26100.0/x86/appxsip.dll", "bin/10.0.26100.0/x86/en-US/AppxPackaging.dll.mui", "bin/10.0.26100.0/x86/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.26100.0/x86/en/WinAppDeployCmd.resources.dll", "bin/10.0.26100.0/x86/ipoverusb.discoverpartners.dll", "bin/10.0.26100.0/x86/makeappx.exe", "bin/10.0.26100.0/x86/makecat.exe", "bin/10.0.26100.0/x86/makecat.exe.manifest", "bin/10.0.26100.0/x86/makepri.exe", "bin/10.0.26100.0/x86/mc.exe", "bin/10.0.26100.0/x86/mdmerge.exe", "bin/10.0.26100.0/x86/midl.exe", "bin/10.0.26100.0/x86/midlc.exe", "bin/10.0.26100.0/x86/midlrt.exe", "bin/10.0.26100.0/x86/midlrtmd.dll", "bin/10.0.26100.0/x86/mrmsupport.dll", "bin/10.0.26100.0/x86/msisip.dll", "bin/10.0.26100.0/x86/mssign32.dll", "bin/10.0.26100.0/x86/mt.exe", "bin/10.0.26100.0/x86/mt.exe.config", "bin/10.0.26100.0/x86/opcservices.dll", "bin/10.0.26100.0/x86/rc.exe", "bin/10.0.26100.0/x86/rcdll.dll", "bin/10.0.26100.0/x86/signtool.exe", "bin/10.0.26100.0/x86/signtool.exe.manifest", "bin/10.0.26100.0/x86/tracewpp.exe", "bin/10.0.26100.0/x86/uuidgen.exe", "bin/10.0.26100.0/x86/winmdidl.exe", "bin/10.0.26100.0/x86/wintrust.dll", "bin/10.0.26100.0/x86/wintrust.dll.ini", "build/Microsoft.Windows.SDK.BuildTools.props", "build/Microsoft.Windows.SDK.BuildTools.targets", "buildTransitive/Microsoft.Windows.SDK.BuildTools.props", "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets", "microsoft.windows.sdk.buildtools.10.0.26100.4948.nupkg.sha512", "microsoft.windows.sdk.buildtools.nuspec", "schemas/10.0.26100.0/winrt/AppxManifestSchema.xsd", "schemas/10.0.26100.0/winrt/AppxManifestSchema2010_v2.xsd", "schemas/10.0.26100.0/winrt/AppxManifestSchema2013.xsd", "schemas/10.0.26100.0/winrt/FoundationManifestSchema.xsd", "schemas/10.0.26100.0/winrt/FoundationManifestSchema_v2.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v10.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v11.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v12.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v13.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v15.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v15a.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v16.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v17.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v18.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v2.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v3.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v4.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v5.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v6.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v7.xsd", "schemas/10.0.26100.0/winrt/UapManifestSchema_v8.xsd"]}, "Microsoft.Windows.SDK.BuildTools.MSIX/1.7.20250829.1": {"sha512": "IMdvRmCIZnBS5GkYnv0po1bcx6U1OF39pqA4TphQ9evDzpCRoSE19/PkDvlUNNrBavTsLIEJgd/TAIFner75ow==", "type": "package", "path": "microsoft.windows.sdk.buildtools.msix/1.7.20250829.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "CodeSignSummary-537b207f-b567-40b5-8f47-b95c567fae6c.md", "CodeSignSummary-b140a053-633e-42ca-8749-f25e57218abb.md", "NOTICE.txt", "build/AppDevPackageScripts/Add-AppDevPackage.ps1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/cs-CZ/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/de-DE/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/en-US/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/es-ES/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/fr-FR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/it-IT/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ja-JP/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ko-KR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/pl-PL/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/pt-BR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ru-RU/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/tr-TR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/zh-CN/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/zh-TW/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Install.ps1", "build/AppDevPackageScripts/LogSideloadingTelemetry.ps1", "build/Landing/extras/br.png", "build/Landing/extras/br_snippet.png", "build/Landing/image.png", "build/Landing/index.template.html", "build/Landing/logo.png", "build/Landing/style.css", "build/Microsoft.Windows.SDK.BuildTools.MSIX.BeforeCommon.props", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Common.props", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Common.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Cpp.props", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Cpp.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Cs.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.DesignTime.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.MrtCore.PriExpansion.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.MrtCore.PriGen.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.MrtCore.Tasks.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.MrtCore.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Packaging.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.Pri.targets", "build/Microsoft.Windows.SDK.BuildTools.MSIX.props", "build/Microsoft.Windows.SDK.BuildTools.MSIX.targets", "build/ProjectItemsSchema.xaml", "build/Rules/MsixPackageDebugPropertyPage.xaml", "build/Rules/WindowsPackageTypePropertyPage.xaml", "build/Templates/Package.appinstaller", "buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.props", "buildTransitive/Microsoft.Windows.SDK.BuildTools.MSIX.targets", "microsoft.windows.sdk.buildtools.msix.1.7.20250829.1.nupkg.sha512", "microsoft.windows.sdk.buildtools.msix.nuspec", "nuget_icon.png", "sdk_license.txt", "tools/net472/Microsoft.Cci.dll", "tools/net472/Microsoft.VisualStudio.RemoteControl.dll", "tools/net472/Microsoft.VisualStudio.Telemetry.dll", "tools/net472/Microsoft.VisualStudio.Utilities.Internal.dll", "tools/net472/Microsoft.Windows.SDK.BuildTools.MSIX.dll", "tools/net472/Newtonsoft.Json.dll", "tools/net6.0/Microsoft.Cci.dll", "tools/net6.0/Microsoft.VisualStudio.RemoteControl.dll", "tools/net6.0/Microsoft.VisualStudio.Telemetry.dll", "tools/net6.0/Microsoft.VisualStudio.Utilities.Internal.dll", "tools/net6.0/Microsoft.Windows.SDK.BuildTools.MSIX.dll", "tools/net6.0/Newtonsoft.Json.dll"]}, "Microsoft.WindowsAppSDK/1.8.250907003": {"sha512": "FCTiOXXnp9EGvVAuLtQc9LT41kj4JZ1Nis9pTrNCubjOrIQAzpJdA3OfWuFCMktsx/s/nWbpQ1JQ4jUAQQDoLA==", "type": "package", "path": "microsoft.windowsappsdk/1.8.250907003", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "build/Microsoft.WindowsAppSDK.props", "build/Microsoft.WindowsAppSDK.targets", "build/native/Microsoft.WindowsAppSDK.props", "build/native/Microsoft.WindowsAppSDK.targets", "buildTransitive/Microsoft.WindowsAppSDK.props", "buildTransitive/Microsoft.WindowsAppSDK.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.props", "buildTransitive/native/Microsoft.WindowsAppSDK.targets", "license.txt", "microsoft.windowsappsdk.1.8.250907003.nupkg.sha512", "microsoft.windowsappsdk.nuspec"]}, "Microsoft.WindowsAppSDK.AI/1.8.37": {"sha512": "WvH7ur+R2N8c3deB8y7q7+Wwx7zybkC6LMS/KNqSYXlSOr75/WCZYwqwrPHJ/63YIUVhka7nJos9g4rIe7SFCw==", "type": "package", "path": "microsoft.windowsappsdk.ai/1.8.37", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.WindowsAppSDK.AI.props", "build/Microsoft.WindowsAppSDK.AI.targets", "build/native/Microsoft.WindowsAppSDK.AI.props", "build/native/Microsoft.WindowsAppSDK.AI.targets", "buildTransitive/Microsoft.WindowsAppSDK.AI.props", "buildTransitive/Microsoft.WindowsAppSDK.AI.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.AI.props", "buildTransitive/native/Microsoft.WindowsAppSDK.AI.targets", "lib/net6.0-windows10.0.17763.0/Microsoft.Graphics.Imaging.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.ContentSafety.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Foundation.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Imaging.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AI.Text.Projection.dll", "license.txt", "metadata/Microsoft.Graphics.Imaging.winmd", "metadata/Microsoft.Windows.AI.ContentSafety.winmd", "metadata/Microsoft.Windows.AI.Foundation.winmd", "metadata/Microsoft.Windows.AI.Imaging.winmd", "metadata/Microsoft.Windows.AI.Text.winmd", "metadata/Microsoft.Windows.AI.winmd", "metadata/Microsoft.Windows.Workloads.winmd", "microsoft.windowsappsdk.ai.1.8.37.nupkg.sha512", "microsoft.windowsappsdk.ai.nuspec", "runtimes-framework/package.appxfragment", "runtimes-framework/win-arm64/native/Microsoft.Graphics.Imaging.dll", "runtimes-framework/win-arm64/native/Microsoft.Graphics.ImagingInternal.ImageObjectRemover.winmd", "runtimes-framework/win-arm64/native/Microsoft.Graphics.ImagingInternal.winmd", "runtimes-framework/win-arm64/native/Microsoft.Graphics.Internal.Imaging.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.ContentModerationInternal.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.ContentSafety.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.FoundationInternal.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.GenerativeInternal.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.Imaging.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.AI.Text.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.Internal.AI.ContentModeration.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.Internal.AI.Generative.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.Internal.Vision.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.Private.Workloads.SessionManager.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.PrivateCommon.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.SemanticSearch.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.Vision.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.VisionInternal.winmd", "runtimes-framework/win-arm64/native/Microsoft.Windows.Workloads.Resources.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.Workloads.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.Workloads.pri", "runtimes-framework/win-arm64/native/NpuDetect/NPUDetect.dll", "runtimes-framework/win-arm64/native/SessionHandleIPCProxyStub.dll", "runtimes-framework/win-arm64/native/workloads.json", "runtimes-framework/win-arm64/native/workloads.qnn.json", "runtimes-framework/win-arm64ec/native/Microsoft.Graphics.Imaging.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Graphics.ImagingInternal.ImageObjectRemover.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Graphics.ImagingInternal.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Graphics.Internal.Imaging.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.ContentModerationInternal.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.ContentSafety.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.FoundationInternal.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.GenerativeInternal.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.Imaging.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.AI.Text.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Internal.AI.ContentModeration.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Internal.AI.Generative.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Internal.Vision.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Private.Workloads.SessionManager.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.PrivateCommon.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.SemanticSearch.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Vision.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.VisionInternal.winmd", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Workloads.Resources.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Workloads.Resources_ec.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Workloads.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Workloads.pri", "runtimes-framework/win-arm64ec/native/NpuDetect/NPUDetect.dll", "runtimes-framework/win-arm64ec/native/SessionHandleIPCProxyStub.dll", "runtimes-framework/win-arm64ec/native/workloads.365.json", "runtimes-framework/win-arm64ec/native/workloads.json", "runtimes-framework/win-arm64ec/native/workloads.lnl.json", "runtimes-framework/win-arm64ec/native/workloads.qnn.json", "runtimes-framework/win-arm64ec/native/workloads.stx.json", "runtimes-framework/win-x64/native/Microsoft.Graphics.Imaging.dll", "runtimes-framework/win-x64/native/Microsoft.Graphics.ImagingInternal.ImageObjectRemover.winmd", "runtimes-framework/win-x64/native/Microsoft.Graphics.ImagingInternal.winmd", "runtimes-framework/win-x64/native/Microsoft.Graphics.Internal.Imaging.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.ContentModerationInternal.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.ContentSafety.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.FoundationInternal.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.GenerativeInternal.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.Imaging.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.AI.Text.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.Internal.AI.ContentModeration.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.Internal.AI.Generative.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.Internal.Vision.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.Private.Workloads.SessionManager.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.PrivateCommon.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.SemanticSearch.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.Vision.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.VisionInternal.winmd", "runtimes-framework/win-x64/native/Microsoft.Windows.Workloads.Resources.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.Workloads.Resources_ec.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.Workloads.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.Workloads.pri", "runtimes-framework/win-x64/native/NpuDetect/NPUDetect.dll", "runtimes-framework/win-x64/native/SessionHandleIPCProxyStub.dll", "runtimes-framework/win-x64/native/workloads.365.json", "runtimes-framework/win-x64/native/workloads.json", "runtimes-framework/win-x64/native/workloads.lnl.json", "runtimes-framework/win-x64/native/workloads.qnn.json", "runtimes-framework/win-x64/native/workloads.stx.json"]}, "Microsoft.WindowsAppSDK.Base/1.8.250831001": {"sha512": "8LlfXBS2Hpw+OoVXViJmIOPXl0nMbqMaFR3j6+QHFNc62VULwPEcXiMRcP2WbV/+mtC7W2LH6yx6uu/Hrr9lVw==", "type": "package", "path": "microsoft.windowsappsdk.base/1.8.250831001", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "build/Microsoft.WindowsAppSDK.Base.props", "build/Microsoft.WindowsAppSDK.Base.targets", "build/Microsoft.WindowsAppSDK.SelfContained.targets", "build/Microsoft.WindowsAppSDK.SingleFile.targets", "build/Microsoft.WindowsAppSDK.SingleProject.targets", "build/Microsoft.WindowsAppSDK.arm64ec.targets", "build/native/Microsoft.WindowsAppSDK.Base.props", "build/native/Microsoft.WindowsAppSDK.Base.targets", "buildTransitive/Microsoft.WindowsAppSDK.Base.props", "buildTransitive/Microsoft.WindowsAppSDK.Base.targets", "buildTransitive/Microsoft.WindowsAppSDK.SelfContained.targets", "buildTransitive/Microsoft.WindowsAppSDK.SingleFile.targets", "buildTransitive/Microsoft.WindowsAppSDK.SingleProject.targets", "buildTransitive/Microsoft.WindowsAppSDK.arm64ec.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.Base.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Base.targets", "license.txt", "microsoft.windowsappsdk.base.1.8.250831001.nupkg.sha512", "microsoft.windowsappsdk.base.nuspec"]}, "Microsoft.WindowsAppSDK.DWrite/1.8.25090401": {"sha512": "WJ0p9yMgiNYqU2O5ZKCXcb7FBjryIUUopgeYMvnlf1yBUYgdjMFMkoJqYVqkz866wnntiB2IZhLxEzhFzvVs1A==", "type": "package", "path": "microsoft.windowsappsdk.dwrite/1.8.25090401", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.WindowsAppSDK.DWrite.ProjectCapabilities.props", "build/Microsoft.WindowsAppSDK.DWrite.props", "build/Microsoft.WindowsAppSDK.DWrite.targets", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.ProjectCapabilities.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.targets", "include/dwrite.h", "include/dwrite_1.h", "include/dwrite_2.h", "include/dwrite_3.h", "include/dwrite_core.h", "lib/native/arm64/DWriteCore.lib", "lib/native/arm64ec/DWriteCore.lib", "lib/native/x64/DWriteCore.lib", "lib/native/x86/DWriteCore.lib", "license.txt", "microsoft.windowsappsdk.dwrite.1.8.25090401.nupkg.sha512", "microsoft.windowsappsdk.dwrite.nuspec", "runtimes-framework/win-arm64/native/DWriteCore.dll", "runtimes-framework/win-arm64ec/native/DWriteCore.dll", "runtimes-framework/win-x64/native/DWriteCore.dll", "runtimes-framework/win-x86/native/DWriteCore.dll"]}, "Microsoft.WindowsAppSDK.Foundation/1.8.250906002": {"sha512": "ltIXeHUX0AATpqmx/oBcRK+zhtK0KAfoGqItlQRlef9kG7Itj9iXAI+1EdFr4cQYzHzFM3PPLszEWDyR633svA==", "type": "package", "path": "microsoft.windowsappsdk.foundation/1.8.250906002", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.WindowsAppSDK.AutoInitializer.CS.targets", "build/Microsoft.WindowsAppSDK.AutoInitializerCommon.targets", "build/Microsoft.WindowsAppSDK.Bootstrap.CS.targets", "build/Microsoft.WindowsAppSDK.BootstrapCommon.targets", "build/Microsoft.WindowsAppSDK.CompatibilitySetter.CS.targets", "build/Microsoft.WindowsAppSDK.DeploymentManager.CS.targets", "build/Microsoft.WindowsAppSDK.DeploymentManagerCommon.targets", "build/Microsoft.WindowsAppSDK.Foundation.props", "build/Microsoft.WindowsAppSDK.Foundation.targets", "build/Microsoft.WindowsAppSDK.UndockedRegFreeWinRT.CS.targets", "build/Microsoft.WindowsAppSDK.UndockedRegFreeWinRTCommon.targets", "build/README.md", "build/native/Microsoft.WindowsAppSDK.Foundation.props", "build/native/Microsoft.WindowsAppSDK.Foundation.targets", "build/native/MrtCore.C.props", "build/native/MrtCore.props", "build/native/WindowsAppSDK-Nuget-Native.AutoInitializer.targets", "build/native/WindowsAppSDK-Nuget-Native.Bootstrap.targets", "build/native/WindowsAppSDK-Nuget-Native.C.props", "build/native/WindowsAppSDK-Nuget-Native.CompatibilitySetter.targets", "build/native/WindowsAppSDK-Nuget-Native.DeploymentManager.targets", "build/native/WindowsAppSDK-Nuget-Native.UndockedRegFreeWinRT.targets", "build/native/WindowsAppSDK-Nuget-Native.WinRt.props", "buildTransitive/Microsoft.WindowsAppSDK.AutoInitializer.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.AutoInitializerCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Bootstrap.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.BootstrapCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.CompatibilitySetter.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.DeploymentManager.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.DeploymentManagerCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Foundation.props", "buildTransitive/Microsoft.WindowsAppSDK.Foundation.targets", "buildTransitive/Microsoft.WindowsAppSDK.UndockedRegFreeWinRT.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.UndockedRegFreeWinRTCommon.targets", "buildTransitive/README.md", "buildTransitive/native/Microsoft.WindowsAppSDK.Foundation.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Foundation.targets", "buildTransitive/native/MrtCore.C.props", "buildTransitive/native/MrtCore.props", "buildTransitive/native/WindowsAppSDK-Nuget-Native.AutoInitializer.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.Bootstrap.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.C.props", "buildTransitive/native/WindowsAppSDK-Nuget-Native.CompatibilitySetter.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.DeploymentManager.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.UndockedRegFreeWinRT.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.WinRt.props", "include/DeploymentManagerAutoInitializer.cpp", "include/DeploymentManagerAutoInitializer.cs", "include/MRM.h", "include/MddBootstrap.h", "include/MddBootstrapAutoInitializer.cpp", "include/MddBootstrapAutoInitializer.cs", "include/Microsoft.Windows.ApplicationModel.Resources.idl", "include/MsixDynamicDependency.h", "include/Security.AccessControl.h", "include/UndockedRegFreeWinRT-AutoInitializer.cpp", "include/UndockedRegFreeWinRT-AutoInitializer.cs", "include/WindowsAppRuntimeAutoInitializer.cpp", "include/WindowsAppRuntimeAutoInitializer.cs", "include/WindowsAppRuntimeInsights.h", "include/decimal.h", "include/decimalcppwinrt.h", "include/wil_msixdynamicdependency.h", "lib/native/arm64/MRM.lib", "lib/native/arm64/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/native/arm64/Microsoft.WindowsAppRuntime.lib", "lib/native/arm64ec/MRM.lib", "lib/native/arm64ec/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/native/arm64ec/Microsoft.WindowsAppRuntime.lib", "lib/native/x64/MRM.lib", "lib/native/x64/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/native/x64/Microsoft.WindowsAppRuntime.lib", "lib/native/x86/MRM.lib", "lib/native/x86/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/native/x86/Microsoft.WindowsAppRuntime.lib", "lib/net6.0-windows10.0.17763.0/Microsoft.Security.Authentication.OAuth.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Background.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.BadgeNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Foundation.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Media.Capture.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Pickers.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Storage.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "license.txt", "metadata/Microsoft.Security.Authentication.OAuth.winmd", "metadata/Microsoft.Windows.AppLifecycle.winmd", "metadata/Microsoft.Windows.AppLifecycle.xml", "metadata/Microsoft.Windows.AppNotifications.Builder.winmd", "metadata/Microsoft.Windows.AppNotifications.Builder.xml", "metadata/Microsoft.Windows.AppNotifications.winmd", "metadata/Microsoft.Windows.AppNotifications.xml", "metadata/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.winmd", "metadata/Microsoft.Windows.ApplicationModel.Background.winmd", "metadata/Microsoft.Windows.ApplicationModel.DynamicDependency.winmd", "metadata/Microsoft.Windows.ApplicationModel.DynamicDependency.xml", "metadata/Microsoft.Windows.ApplicationModel.Resources.winmd", "metadata/Microsoft.Windows.ApplicationModel.Resources.xml", "metadata/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.winmd", "metadata/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.xml", "metadata/Microsoft.Windows.BadgeNotifications.winmd", "metadata/Microsoft.Windows.Foundation.winmd", "metadata/Microsoft.Windows.Globalization.winmd", "metadata/Microsoft.Windows.Globalization.xml", "metadata/Microsoft.Windows.Management.Deployment.winmd", "metadata/Microsoft.Windows.Management.Deployment.xml", "metadata/Microsoft.Windows.Media.Capture.winmd", "metadata/Microsoft.Windows.PushNotifications.winmd", "metadata/Microsoft.Windows.PushNotifications.xml", "metadata/Microsoft.Windows.Security.AccessControl.winmd", "metadata/Microsoft.Windows.Security.AccessControl.xml", "metadata/Microsoft.Windows.Storage.Pickers.winmd", "metadata/Microsoft.Windows.Storage.winmd", "metadata/Microsoft.Windows.Storage.xml", "metadata/Microsoft.Windows.System.Power.winmd", "metadata/Microsoft.Windows.System.Power.xml", "metadata/Microsoft.Windows.System.winmd", "metadata/Microsoft.Windows.System.xml", "microsoft.windowsappsdk.foundation.1.8.250906002.nupkg.sha512", "microsoft.windowsappsdk.foundation.nuspec", "runtimes-framework/package.appxfragment", "runtimes-framework/win-arm64/native/DeploymentAgent.exe", "runtimes-framework/win-arm64/native/MRM.dll", "runtimes-framework/win-arm64/native/Microsoft.Windows.ApplicationModel.Resources.dll", "runtimes-framework/win-arm64/native/Microsoft.WindowsAppRuntime.dll", "runtimes-framework/win-arm64/native/Microsoft.WindowsAppRuntime.pri", "runtimes-framework/win-arm64/native/PushNotificationsLongRunningTask.ProxyStub.dll", "runtimes-framework/win-arm64/native/RestartAgent.exe", "runtimes-framework/win-arm64ec/native/DeploymentAgent.exe", "runtimes-framework/win-arm64ec/native/MRM.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.ApplicationModel.Resources.dll", "runtimes-framework/win-arm64ec/native/Microsoft.WindowsAppRuntime.dll", "runtimes-framework/win-arm64ec/native/Microsoft.WindowsAppRuntime.pri", "runtimes-framework/win-arm64ec/native/PushNotificationsLongRunningTask.ProxyStub.dll", "runtimes-framework/win-arm64ec/native/RestartAgent.exe", "runtimes-framework/win-x64/native/DeploymentAgent.exe", "runtimes-framework/win-x64/native/MRM.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.ApplicationModel.Resources.dll", "runtimes-framework/win-x64/native/Microsoft.WindowsAppRuntime.dll", "runtimes-framework/win-x64/native/Microsoft.WindowsAppRuntime.pri", "runtimes-framework/win-x64/native/PushNotificationsLongRunningTask.ProxyStub.dll", "runtimes-framework/win-x64/native/RestartAgent.exe", "runtimes-framework/win-x86/native/DeploymentAgent.exe", "runtimes-framework/win-x86/native/MRM.dll", "runtimes-framework/win-x86/native/Microsoft.Windows.ApplicationModel.Resources.dll", "runtimes-framework/win-x86/native/Microsoft.WindowsAppRuntime.dll", "runtimes-framework/win-x86/native/Microsoft.WindowsAppRuntime.pri", "runtimes-framework/win-x86/native/PushNotificationsLongRunningTask.ProxyStub.dll", "runtimes-framework/win-x86/native/RestartAgent.exe", "runtimes/win-arm64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-arm64ec/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-arm64ec/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-x64/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-x86/native/Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll", "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll"]}, "Microsoft.WindowsAppSDK.InteractiveExperiences/1.8.250906004": {"sha512": "UoK2yeZiycD1DmADHZz+hcMAoOaUfXLc9qUPfOjmVeKQ6i5ghGMjx/nd49bksP3wVhmSGHxb3argRKWPkK5maw==", "type": "package", "path": "microsoft.windowsappsdk.interactiveexperiences/1.8.250906004", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.InteractiveExperiences.Capabilities.props", "build/Microsoft.InteractiveExperiences.Capabilities.targets", "build/Microsoft.InteractiveExperiences.Common.props", "build/Microsoft.InteractiveExperiences.Common.targets", "build/Microsoft.InteractiveExperiences.EC.Capabilities.props", "build/Microsoft.InteractiveExperiences.EC.Capabilities.targets", "build/Microsoft.InteractiveExperiences.EC.Common.props", "build/Microsoft.InteractiveExperiences.EC.Common.targets", "build/Microsoft.InteractiveExperiences.EC.props", "build/Microsoft.InteractiveExperiences.EC.targets", "build/Microsoft.InteractiveExperiences.props", "build/Microsoft.InteractiveExperiences.targets", "build/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "build/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "build/native/Microsoft.InteractiveExperiences.EC.props", "build/native/Microsoft.InteractiveExperiences.EC.targets", "build/native/Microsoft.InteractiveExperiences.props", "build/native/Microsoft.InteractiveExperiences.targets", "build/native/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "build/native/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "buildTransitive/Microsoft.InteractiveExperiences.Capabilities.props", "buildTransitive/Microsoft.InteractiveExperiences.Capabilities.targets", "buildTransitive/Microsoft.InteractiveExperiences.Common.props", "buildTransitive/Microsoft.InteractiveExperiences.Common.targets", "buildTransitive/Microsoft.InteractiveExperiences.EC.Capabilities.props", "buildTransitive/Microsoft.InteractiveExperiences.EC.Capabilities.targets", "buildTransitive/Microsoft.InteractiveExperiences.EC.Common.props", "buildTransitive/Microsoft.InteractiveExperiences.EC.Common.targets", "buildTransitive/Microsoft.InteractiveExperiences.EC.props", "buildTransitive/Microsoft.InteractiveExperiences.EC.targets", "buildTransitive/Microsoft.InteractiveExperiences.props", "buildTransitive/Microsoft.InteractiveExperiences.targets", "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "buildTransitive/native/Microsoft.InteractiveExperiences.EC.props", "buildTransitive/native/Microsoft.InteractiveExperiences.EC.targets", "buildTransitive/native/Microsoft.InteractiveExperiences.props", "buildTransitive/native/Microsoft.InteractiveExperiences.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "buildTransitive/native/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "include/Microsoft.UI.Composition.Interop.h", "include/Microsoft.UI.Designer.Interop.h", "include/Microsoft.UI.Dispatching.Interop.h", "include/Microsoft.UI.Input.InputCursor.Interop.h", "include/Microsoft.UI.Input.InputPreTranslateSource.Interop.h", "include/Microsoft.UI.Interop.h", "include/winrt/Microsoft.UI.Composition.Interop.h", "include/winrt/Microsoft.UI.Designer.Interop.h", "include/winrt/Microsoft.UI.Dispatching.Interop.h", "include/winrt/Microsoft.UI.Input.InputCursor.Interop.h", "include/winrt/Microsoft.UI.Input.InputPreTranslateSource.Interop.h", "include/winrt/Microsoft.UI.Interop.h", "lib/native/win10-arm64/Microsoft.UI.Dispatching.lib", "lib/native/win10-arm64ec/Microsoft.UI.Dispatching.lib", "lib/native/win10-x64/Microsoft.UI.Dispatching.lib", "lib/native/win10-x86/Microsoft.UI.Dispatching.lib", "lib/net6.0-windows10.0.17763.0/Microsoft.InteractiveExperiences.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.InteractiveExperiences.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.xml", "license.txt", "metadata/10.0.17763.0/Microsoft.Foundation.winmd", "metadata/10.0.17763.0/Microsoft.Graphics.winmd", "metadata/10.0.17763.0/Microsoft.Graphics.xml", "metadata/10.0.17763.0/Microsoft.UI.winmd", "metadata/10.0.17763.0/Microsoft.UI.xml", "metadata/10.0.18362.0/Microsoft.Foundation.winmd", "metadata/10.0.18362.0/Microsoft.Graphics.winmd", "metadata/10.0.18362.0/Microsoft.Graphics.xml", "metadata/10.0.18362.0/Microsoft.UI.winmd", "metadata/10.0.18362.0/Microsoft.UI.xml", "microsoft.windowsappsdk.interactiveexperiences.1.8.250906004.nupkg.sha512", "microsoft.windowsappsdk.interactiveexperiences.nuspec", "runtimes-framework/package.appxfragment", "runtimes-framework/win-arm64/native/CoreMessagingXP.dll", "runtimes-framework/win-arm64/native/DwmSceneI.dll", "runtimes-framework/win-arm64/native/Microsoft.DirectManipulation.dll", "runtimes-framework/win-arm64/native/Microsoft.Graphics.Display.dll", "runtimes-framework/win-arm64/native/Microsoft.InputStateManager.dll", "runtimes-framework/win-arm64/native/Microsoft.Internal.FrameworkUdk.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Composition.OSSupport.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Designer.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Input.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Windowing.Core.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Windowing.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.pri", "runtimes-framework/win-arm64/native/dcompi.dll", "runtimes-framework/win-arm64/native/dwmcorei.dll", "runtimes-framework/win-arm64/native/marshal.dll", "runtimes-framework/win-arm64/native/wuceffectsi.dll", "runtimes-framework/win-arm64ec/native/CoreMessagingXP.dll", "runtimes-framework/win-arm64ec/native/DwmSceneI.dll", "runtimes-framework/win-arm64ec/native/Microsoft.DirectManipulation.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Graphics.Display.dll", "runtimes-framework/win-arm64ec/native/Microsoft.InputStateManager.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Internal.FrameworkUdk.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Composition.OSSupport.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Designer.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Input.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Windowing.Core.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Windowing.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.pri", "runtimes-framework/win-arm64ec/native/dcompi.dll", "runtimes-framework/win-arm64ec/native/dwmcorei.dll", "runtimes-framework/win-arm64ec/native/marshal.dll", "runtimes-framework/win-arm64ec/native/wuceffectsi.dll", "runtimes-framework/win-x64/native/CoreMessagingXP.dll", "runtimes-framework/win-x64/native/DwmSceneI.dll", "runtimes-framework/win-x64/native/Microsoft.DirectManipulation.dll", "runtimes-framework/win-x64/native/Microsoft.Graphics.Display.dll", "runtimes-framework/win-x64/native/Microsoft.InputStateManager.dll", "runtimes-framework/win-x64/native/Microsoft.Internal.FrameworkUdk.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Composition.OSSupport.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Designer.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Input.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Windowing.Core.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Windowing.dll", "runtimes-framework/win-x64/native/Microsoft.UI.dll", "runtimes-framework/win-x64/native/Microsoft.UI.pri", "runtimes-framework/win-x64/native/dcompi.dll", "runtimes-framework/win-x64/native/dwmcorei.dll", "runtimes-framework/win-x64/native/marshal.dll", "runtimes-framework/win-x64/native/wuceffectsi.dll", "runtimes-framework/win-x86/native/CoreMessagingXP.dll", "runtimes-framework/win-x86/native/DwmSceneI.dll", "runtimes-framework/win-x86/native/Microsoft.DirectManipulation.dll", "runtimes-framework/win-x86/native/Microsoft.Graphics.Display.dll", "runtimes-framework/win-x86/native/Microsoft.InputStateManager.dll", "runtimes-framework/win-x86/native/Microsoft.Internal.FrameworkUdk.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Composition.OSSupport.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Designer.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Input.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Windowing.Core.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Windowing.dll", "runtimes-framework/win-x86/native/Microsoft.UI.dll", "runtimes-framework/win-x86/native/Microsoft.UI.pri", "runtimes-framework/win-x86/native/dcompi.dll", "runtimes-framework/win-x86/native/dwmcorei.dll", "runtimes-framework/win-x86/native/marshal.dll", "runtimes-framework/win-x86/native/wuceffectsi.dll"]}, "Microsoft.WindowsAppSDK.Runtime/1.8.250907003": {"sha512": "URsthdat9pv1wnRNAy0WA5yejsc47QsSjjJ+L6INEgIFilrp4/LYndpHkoWh3KwBSjwkskvZlSprbOl09YVg/g==", "type": "package", "path": "microsoft.windowsappsdk.runtime/1.8.250907003", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "WindowsAppSDK-VersionInfo.json", "WindowsAppSDK-VersionInfo.xml", "build/Microsoft.WindowsAppSDK.AppXReference.props", "build/Microsoft.WindowsAppSDK.ComponentReference.targets", "build/Microsoft.WindowsAppSDK.Runtime.props", "build/Microsoft.WindowsAppSDK.Runtime.targets", "build/MicrosoftWindowsAppSDKFoundationAppXVersion.props", "build/native/Microsoft.WindowsAppSDK.Runtime.props", "build/native/Microsoft.WindowsAppSDK.Runtime.targets", "buildTransitive/Microsoft.WindowsAppSDK.AppXReference.props", "buildTransitive/Microsoft.WindowsAppSDK.ComponentReference.targets", "buildTransitive/Microsoft.WindowsAppSDK.Runtime.props", "buildTransitive/Microsoft.WindowsAppSDK.Runtime.targets", "buildTransitive/MicrosoftWindowsAppSDKFoundationAppXVersion.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Runtime.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Runtime.targets", "include/WindowsAppSDK-VersionInfo.cs", "include/WindowsAppSDK-VersionInfo.h", "license.txt", "microsoft.windowsappsdk.runtime.1.8.250907003.nupkg.sha512", "microsoft.windowsappsdk.runtime.nuspec", "tools/MSIX/win10-arm64/MSIX.inventory", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.1.8.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.DDLM.1.8.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.Main.1.8.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.Singleton.1.8.msix", "tools/MSIX/win10-arm64ec/MSIX.inventory", "tools/MSIX/win10-arm64ec/Microsoft.WindowsAppRuntime.1.8.msix", "tools/MSIX/win10-x64/MSIX.inventory", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.1.8.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.DDLM.1.8.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.Main.1.8.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.Singleton.1.8.msix", "tools/MSIX/win10-x86/MSIX.inventory", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.1.8.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.DDLM.1.8.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.Main.1.8.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.Singleton.1.8.msix"]}, "Microsoft.WindowsAppSDK.Widgets/1.8.250904007": {"sha512": "sgwdXYhb8S4JjBmWWiFxALT1xK0fJeAbisolctmodMX7tlvBXDgUyvl/GHfTQ61DGIiW+kokX61WR46L2YlhAA==", "type": "package", "path": "microsoft.windowsappsdk.widgets/1.8.250904007", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.WindowsAppSDK.Widgets.props", "build/Microsoft.WindowsAppSDK.Widgets.targets", "build/native/Microsoft.WindowsAppSDK.Widgets.props", "build/native/Microsoft.WindowsAppSDK.Widgets.targets", "buildTransitive/Microsoft.WindowsAppSDK.Widgets.props", "buildTransitive/Microsoft.WindowsAppSDK.Widgets.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.Widgets.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Widgets.targets", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.xml", "license.txt", "metadata/Microsoft.Windows.Widgets.winmd", "metadata/Microsoft.Windows.Widgets.xml", "microsoft.windowsappsdk.widgets.1.8.250904007.nupkg.sha512", "microsoft.windowsappsdk.widgets.nuspec", "runtimes-framework/package.appxfragment", "runtimes-framework/win-arm64/native/Microsoft.Windows.Widgets.dll", "runtimes-framework/win-arm64ec/native/Microsoft.Windows.Widgets.dll", "runtimes-framework/win-x64/native/Microsoft.Windows.Widgets.dll", "runtimes-framework/win-x86/native/Microsoft.Windows.Widgets.dll"]}, "Microsoft.WindowsAppSDK.WinUI/1.8.250906003": {"sha512": "6oskwUluqlDGwUcwYlY3GWTMLajyjh9e790SmWzCCMDRV6sunYbqp7DkiSLzn8nhgSbGvmj6zG92JnkYRlbrXw==", "type": "package", "path": "microsoft.windowsappsdk.winui/1.8.250906003", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "NOTICE.txt", "build/Microsoft.UI.Xaml.Markup.Compiler.BeforeCommon.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.interop.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.props", "build/Microsoft.UI.Xaml.Markup.Compiler.targets", "build/Microsoft.WinUI.AppX.targets", "build/Microsoft.WinUI.NET.Markup.Compiler.targets", "build/Microsoft.WinUI.ProjectCapabilities.props", "build/Microsoft.WinUI.References.targets", "build/Microsoft.WinUI.props", "build/Microsoft.WinUI.targets", "build/Microsoft.WindowsAppSDK.WinUI.props", "build/Microsoft.WindowsAppSDK.WinUI.targets", "build/native/LiftedWinRTClassRegistrations.xml", "build/native/Microsoft.WinUI.References.targets", "build/native/Microsoft.WinUI.props", "build/native/Microsoft.WinUI.targets", "build/native/Microsoft.WindowsAppSDK.WinUI.props", "build/native/Microsoft.WindowsAppSDK.WinUI.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.BeforeCommon.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.interop.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.props", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.targets", "buildTransitive/Microsoft.WinUI.AppX.targets", "buildTransitive/Microsoft.WinUI.NET.Markup.Compiler.targets", "buildTransitive/Microsoft.WinUI.ProjectCapabilities.props", "buildTransitive/Microsoft.WinUI.References.targets", "buildTransitive/Microsoft.WinUI.props", "buildTransitive/Microsoft.WinUI.targets", "buildTransitive/Microsoft.WindowsAppSDK.WinUI.props", "buildTransitive/Microsoft.WindowsAppSDK.WinUI.targets", "buildTransitive/native/LiftedWinRTClassRegistrations.xml", "buildTransitive/native/Microsoft.WinUI.References.targets", "buildTransitive/native/Microsoft.WinUI.props", "buildTransitive/native/Microsoft.WinUI.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.WinUI.props", "buildTransitive/native/Microsoft.WindowsAppSDK.WinUI.targets", "include/microsoft.ui.xaml.hosting.referencetracker.h", "include/microsoft.ui.xaml.hosting.referencetracker.idl", "include/microsoft.ui.xaml.media.dxinterop.h", "include/microsoft.ui.xaml.media.dxinterop.idl", "include/microsoft.ui.xaml.window.h", "include/microsoft.ui.xaml.window.idl", "include/winrtdirect3d11.h", "include/winrtdirectxcommon.h", "include/xamlom.winui.h", "include/xamlom.winui.idl", "lib/native/Microsoft.UI/Themes/generic.xaml", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI/Themes/generic.xaml", "license.txt", "metadata/Microsoft.UI.Text.winmd", "metadata/Microsoft.UI.Text.xml", "metadata/Microsoft.UI.Xaml.winmd", "metadata/Microsoft.UI.Xaml.xml", "microsoft.windowsappsdk.winui.1.8.250906003.nupkg.sha512", "microsoft.windowsappsdk.winui.nuspec", "runtimes-framework/package.appxfragment", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml.Controls.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml.Controls.pri", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml.Internal.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml.Phone.dll", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml/Assets/NoiseAsset_256x256_PNG.png", "runtimes-framework/win-arm64/native/Microsoft.UI.Xaml/Assets/map.html", "runtimes-framework/win-arm64/native/Microsoft.ui.xaml.dll", "runtimes-framework/win-arm64/native/Microsoft.ui.xaml.resources.19h1.dll", "runtimes-framework/win-arm64/native/Microsoft.ui.xaml.resources.common.dll", "runtimes-framework/win-arm64/native/WinUIEdit.dll", "runtimes-framework/win-arm64/native/af-ZA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/af-ZA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/am-ET/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/am-ET/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ar-SA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ar-SA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/as-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/as-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/az-Latn-AZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/az-Latn-AZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/bg-BG/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/bg-BG/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/bn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/bn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/bs-Latn-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/bs-Latn-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ca-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ca-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ca-Es-VALENCIA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ca-Es-VALENCIA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/cs-CZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/cs-CZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/cy-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/cy-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/da-DK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/da-DK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/de-DE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/de-DE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/el-GR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/el-GR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/en-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/en-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/en-us/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/en-us/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/es-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/es-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/es-MX/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/es-MX/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/et-EE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/et-EE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/eu-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/eu-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/fa-IR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/fa-IR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/fi-FI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/fi-FI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/fil-PH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/fil-PH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/fr-CA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/fr-CA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/fr-FR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/fr-FR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ga-IE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ga-IE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/gd-gb/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/gd-gb/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/gl-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/gl-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/gu-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/gu-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/he-IL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/he-IL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/hi-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/hi-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/hr-HR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/hr-HR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/hu-HU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/hu-HU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/hy-AM/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/hy-AM/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/id-ID/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/id-ID/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/is-IS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/is-IS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/it-IT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/it-IT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ja-JP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ja-JP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ka-GE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ka-GE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/kk-KZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/kk-KZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/km-KH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/km-KH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/kn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/kn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ko-KR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ko-KR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/kok-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/kok-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/lb-LU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/lb-LU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/lo-LA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/lo-LA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/lt-LT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/lt-LT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/lv-LV/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/lv-LV/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/mi-NZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/mi-NZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/mk-MK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/mk-MK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ml-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ml-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/mr-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/mr-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ms-MY/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ms-MY/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/mt-MT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/mt-MT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/nb-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/nb-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ne-NP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ne-NP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/nl-NL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/nl-NL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/nn-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/nn-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/or-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/or-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/pa-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/pa-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/pl-PL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/pl-PL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/pt-BR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/pt-BR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/pt-PT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/pt-PT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/quz-PE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/quz-PE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ro-RO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ro-RO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ru-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ru-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sk-SK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sk-SK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sl-SI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sl-SI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sq-AL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sq-AL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sr-Cyrl-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sr-Cyrl-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sr-Cyrl-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sr-Cyrl-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sr-Latn-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sr-Latn-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/sv-SE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/sv-SE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ta-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ta-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/te-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/te-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/th-TH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/th-TH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/tr-TR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/tr-TR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/tt-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/tt-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ug-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ug-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/uk-UA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/uk-UA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/ur-PK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/ur-PK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/uz-Latn-UZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/uz-Latn-UZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/vi-VN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/vi-VN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/zh-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/zh-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64/native/zh-TW/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64/native/zh-TW/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml.Controls.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml.Controls.pri", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml.Internal.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml.Phone.dll", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml/Assets/NoiseAsset_256x256_PNG.png", "runtimes-framework/win-arm64ec/native/Microsoft.UI.Xaml/Assets/map.html", "runtimes-framework/win-arm64ec/native/Microsoft.ui.xaml.dll", "runtimes-framework/win-arm64ec/native/Microsoft.ui.xaml.resources.19h1.dll", "runtimes-framework/win-arm64ec/native/Microsoft.ui.xaml.resources.common.dll", "runtimes-framework/win-arm64ec/native/WinUIEdit.dll", "runtimes-framework/win-arm64ec/native/af-ZA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/af-ZA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/am-ET/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/am-ET/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ar-SA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ar-SA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/as-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/as-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/az-Latn-AZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/az-Latn-AZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/bg-BG/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/bg-BG/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/bn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/bn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/bs-Latn-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/bs-Latn-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ca-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ca-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ca-Es-VALENCIA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ca-Es-VALENCIA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/cs-CZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/cs-CZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/cy-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/cy-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/da-DK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/da-DK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/de-DE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/de-DE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/el-GR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/el-GR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/en-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/en-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/en-us/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/en-us/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/es-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/es-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/es-MX/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/es-MX/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/et-EE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/et-EE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/eu-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/eu-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/fa-IR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/fa-IR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/fi-FI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/fi-FI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/fil-PH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/fil-PH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/fr-CA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/fr-CA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/fr-FR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/fr-FR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ga-IE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ga-IE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/gd-gb/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/gd-gb/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/gl-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/gl-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/gu-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/gu-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/he-IL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/he-IL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/hi-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/hi-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/hr-HR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/hr-HR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/hu-HU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/hu-HU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/hy-AM/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/hy-AM/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/id-ID/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/id-ID/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/is-IS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/is-IS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/it-IT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/it-IT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ja-JP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ja-JP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ka-GE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ka-GE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/kk-KZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/kk-KZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/km-KH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/km-KH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/kn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/kn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ko-KR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ko-KR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/kok-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/kok-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/lb-LU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/lb-LU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/lo-LA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/lo-LA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/lt-LT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/lt-LT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/lv-LV/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/lv-LV/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/mi-NZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/mi-NZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/mk-MK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/mk-MK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ml-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ml-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/mr-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/mr-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ms-MY/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ms-MY/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/mt-MT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/mt-MT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/nb-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/nb-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ne-NP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ne-NP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/nl-NL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/nl-NL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/nn-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/nn-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/or-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/or-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/pa-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/pa-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/pl-PL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/pl-PL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/pt-BR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/pt-BR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/pt-PT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/pt-PT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/quz-PE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/quz-PE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ro-RO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ro-RO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ru-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ru-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sk-SK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sk-SK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sl-SI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sl-SI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sq-AL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sq-AL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Cyrl-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Cyrl-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Cyrl-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Cyrl-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Latn-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sr-Latn-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/sv-SE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/sv-SE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ta-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ta-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/te-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/te-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/th-TH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/th-TH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/tr-TR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/tr-TR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/tt-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/tt-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ug-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ug-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/uk-UA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/uk-UA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/ur-PK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/ur-PK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/uz-Latn-UZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/uz-Latn-UZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/vi-VN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/vi-VN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/zh-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/zh-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-arm64ec/native/zh-TW/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-arm64ec/native/zh-TW/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml.Controls.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml.Controls.pri", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml.Internal.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml.Phone.dll", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml/Assets/NoiseAsset_256x256_PNG.png", "runtimes-framework/win-x64/native/Microsoft.UI.Xaml/Assets/map.html", "runtimes-framework/win-x64/native/Microsoft.ui.xaml.dll", "runtimes-framework/win-x64/native/Microsoft.ui.xaml.resources.19h1.dll", "runtimes-framework/win-x64/native/Microsoft.ui.xaml.resources.common.dll", "runtimes-framework/win-x64/native/WinUIEdit.dll", "runtimes-framework/win-x64/native/af-ZA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/af-ZA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/am-ET/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/am-ET/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ar-SA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ar-SA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/as-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/as-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/az-Latn-AZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/az-Latn-AZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/bg-BG/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/bg-BG/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/bn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/bn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/bs-Latn-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/bs-Latn-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ca-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ca-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ca-Es-VALENCIA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ca-Es-VALENCIA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/cs-CZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/cs-CZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/cy-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/cy-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/da-DK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/da-DK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/de-DE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/de-DE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/el-GR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/el-GR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/en-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/en-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/en-us/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/en-us/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/es-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/es-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/es-MX/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/es-MX/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/et-EE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/et-EE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/eu-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/eu-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/fa-IR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/fa-IR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/fi-FI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/fi-FI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/fil-PH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/fil-PH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/fr-CA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/fr-CA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/fr-FR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/fr-FR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ga-IE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ga-IE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/gd-gb/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/gd-gb/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/gl-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/gl-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/gu-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/gu-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/he-IL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/he-IL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/hi-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/hi-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/hr-HR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/hr-HR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/hu-HU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/hu-HU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/hy-AM/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/hy-AM/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/id-ID/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/id-ID/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/is-IS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/is-IS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/it-IT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/it-IT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ja-JP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ja-JP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ka-GE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ka-GE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/kk-KZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/kk-KZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/km-KH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/km-KH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/kn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/kn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ko-KR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ko-KR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/kok-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/kok-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/lb-LU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/lb-LU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/lo-LA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/lo-LA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/lt-LT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/lt-LT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/lv-LV/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/lv-LV/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/mi-NZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/mi-NZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/mk-MK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/mk-MK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ml-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ml-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/mr-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/mr-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ms-MY/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ms-MY/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/mt-MT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/mt-MT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/nb-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/nb-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ne-NP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ne-NP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/nl-NL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/nl-NL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/nn-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/nn-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/or-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/or-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/pa-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/pa-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/pl-PL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/pl-PL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/pt-BR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/pt-BR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/pt-PT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/pt-PT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/quz-PE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/quz-PE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ro-RO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ro-RO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ru-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ru-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sk-SK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sk-SK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sl-SI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sl-SI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sq-AL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sq-AL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sr-Cyrl-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sr-Cyrl-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sr-Cyrl-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sr-Cyrl-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sr-Latn-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sr-Latn-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/sv-SE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/sv-SE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ta-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ta-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/te-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/te-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/th-TH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/th-TH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/tr-TR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/tr-TR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/tt-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/tt-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ug-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ug-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/uk-UA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/uk-UA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/ur-PK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/ur-PK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/uz-Latn-UZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/uz-Latn-UZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/vi-VN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/vi-VN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/zh-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/zh-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x64/native/zh-TW/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x64/native/zh-TW/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml.Controls.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml.Controls.pri", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml.Internal.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml.Phone.dll", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml/Assets/NoiseAsset_256x256_PNG.png", "runtimes-framework/win-x86/native/Microsoft.UI.Xaml/Assets/map.html", "runtimes-framework/win-x86/native/Microsoft.ui.xaml.dll", "runtimes-framework/win-x86/native/Microsoft.ui.xaml.resources.19h1.dll", "runtimes-framework/win-x86/native/Microsoft.ui.xaml.resources.common.dll", "runtimes-framework/win-x86/native/WinUIEdit.dll", "runtimes-framework/win-x86/native/af-ZA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/af-ZA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/am-ET/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/am-ET/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ar-SA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ar-SA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/as-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/as-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/az-Latn-AZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/az-Latn-AZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/bg-BG/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/bg-BG/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/bn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/bn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/bs-Latn-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/bs-Latn-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ca-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ca-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ca-Es-VALENCIA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ca-Es-VALENCIA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/cs-CZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/cs-CZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/cy-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/cy-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/da-DK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/da-DK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/de-DE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/de-DE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/el-GR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/el-GR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/en-GB/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/en-GB/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/en-us/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/en-us/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/es-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/es-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/es-MX/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/es-MX/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/et-EE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/et-EE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/eu-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/eu-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/fa-IR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/fa-IR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/fi-FI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/fi-FI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/fil-PH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/fil-PH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/fr-CA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/fr-CA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/fr-FR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/fr-FR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ga-IE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ga-IE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/gd-gb/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/gd-gb/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/gl-ES/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/gl-ES/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/gu-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/gu-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/he-IL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/he-IL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/hi-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/hi-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/hr-HR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/hr-HR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/hu-HU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/hu-HU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/hy-AM/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/hy-AM/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/id-ID/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/id-ID/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/is-IS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/is-IS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/it-IT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/it-IT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ja-JP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ja-JP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ka-GE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ka-GE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/kk-KZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/kk-KZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/km-KH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/km-KH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/kn-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/kn-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ko-KR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ko-KR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/kok-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/kok-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/lb-LU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/lb-LU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/lo-LA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/lo-LA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/lt-LT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/lt-LT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/lv-LV/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/lv-LV/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/mi-NZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/mi-NZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/mk-MK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/mk-MK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ml-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ml-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/mr-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/mr-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ms-MY/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ms-MY/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/mt-MT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/mt-MT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/nb-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/nb-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ne-NP/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ne-NP/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/nl-NL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/nl-NL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/nn-NO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/nn-NO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/or-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/or-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/pa-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/pa-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/pl-PL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/pl-PL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/pt-BR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/pt-BR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/pt-PT/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/pt-PT/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/quz-PE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/quz-PE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ro-RO/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ro-RO/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ru-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ru-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sk-SK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sk-SK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sl-SI/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sl-SI/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sq-AL/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sq-AL/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sr-Cyrl-BA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sr-Cyrl-BA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sr-Cyrl-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sr-Cyrl-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sr-Latn-RS/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sr-Latn-RS/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/sv-SE/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/sv-SE/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ta-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ta-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/te-IN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/te-IN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/th-TH/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/th-TH/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/tr-TR/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/tr-TR/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/tt-RU/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/tt-RU/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ug-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ug-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/uk-UA/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/uk-UA/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/ur-PK/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/ur-PK/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/uz-Latn-UZ/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/uz-Latn-UZ/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/vi-VN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/vi-VN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/zh-CN/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/zh-CN/Microsoft.ui.xaml.dll.mui", "runtimes-framework/win-x86/native/zh-TW/Microsoft.UI.Xaml.Phone.dll.mui", "runtimes-framework/win-x86/native/zh-TW/Microsoft.ui.xaml.dll.mui", "tools/NOTICE.txt", "tools/arm64/GenXbf.dll", "tools/arm64ec/GenXbf.dll", "tools/net472/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net472/Microsoft.Build.Framework.dll", "tools/net472/Microsoft.Build.Utilities.Core.dll", "tools/net472/Microsoft.Build.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.IO.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.MSBuildInterop.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.dll", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/System.Text.Encodings.Web.dll", "tools/net472/System.Text.Json.dll", "tools/net472/System.Threading.Tasks.Dataflow.dll", "tools/net472/System.Threading.Tasks.Extensions.dll", "tools/net472/XamlCompiler.exe", "tools/net472/XamlCompiler.exe.config", "tools/net6.0/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.IO.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.MSBuildInterop.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.dll", "tools/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "tools/net6.0/System.Text.Encodings.Web.dll", "tools/net6.0/System.Text.Json.dll", "tools/x64/GenXbf.dll", "tools/x86/GenXbf.dll"]}, "Mono.TextTemplating/3.0.0": {"sha512": "YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "type": "package", "path": "mono.texttemplating/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt/LICENSE", "buildTransitive/Mono.TextTemplating.targets", "lib/net472/Mono.TextTemplating.dll", "lib/net6.0/Mono.TextTemplating.dll", "lib/netstandard2.0/Mono.TextTemplating.dll", "mono.texttemplating.3.0.0.nupkg.sha512", "mono.texttemplating.nuspec", "readme.md"]}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"sha512": "UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "type": "package", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid90/SQLitePCLRaw.batteries_v2.dll", "lib/net461/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.xml", "lib/net6.0-ios14.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-ios14.2/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-tvos10.0/SQLitePCLRaw.batteries_v2.dll", "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll", "lib/xamarinios10/SQLitePCLRaw.batteries_v2.dll", "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.bundle_e_sqlite3.nuspec"]}, "SQLitePCLRaw.core/2.1.10": {"sha512": "Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "type": "package", "path": "sqlitepclraw.core/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SQLitePCLRaw.core.dll", "sqlitepclraw.core.2.1.10.nupkg.sha512", "sqlitepclraw.core.nuspec"]}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"sha512": "mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "type": "package", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/net461/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net6.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net7.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net8.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net9.0/SQLitePCLRaw.lib.e_sqlite3.targets", "lib/net461/_._", "lib/netstandard2.0/_._", "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net7.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a", "runtimes/linux-arm/native/libe_sqlite3.so", "runtimes/linux-arm64/native/libe_sqlite3.so", "runtimes/linux-armel/native/libe_sqlite3.so", "runtimes/linux-mips64/native/libe_sqlite3.so", "runtimes/linux-musl-arm/native/libe_sqlite3.so", "runtimes/linux-musl-arm64/native/libe_sqlite3.so", "runtimes/linux-musl-s390x/native/libe_sqlite3.so", "runtimes/linux-musl-x64/native/libe_sqlite3.so", "runtimes/linux-ppc64le/native/libe_sqlite3.so", "runtimes/linux-s390x/native/libe_sqlite3.so", "runtimes/linux-x64/native/libe_sqlite3.so", "runtimes/linux-x86/native/libe_sqlite3.so", "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib", "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib", "runtimes/osx-arm64/native/libe_sqlite3.dylib", "runtimes/osx-x64/native/libe_sqlite3.dylib", "runtimes/win-arm/native/e_sqlite3.dll", "runtimes/win-arm64/native/e_sqlite3.dll", "runtimes/win-x64/native/e_sqlite3.dll", "runtimes/win-x86/native/e_sqlite3.dll", "runtimes/win10-arm/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-arm64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x86/nativeassets/uap10.0/e_sqlite3.dll", "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.lib.e_sqlite3.nuspec"]}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"sha512": "uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "type": "package", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/netstandard2.0/SQLitePCLRaw.provider.e_sqlite3.dll", "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.provider.e_sqlite3.nuspec"]}, "System.CodeDom/6.0.0": {"sha512": "CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "type": "package", "path": "system.codedom/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.CodeDom.dll", "lib/net461/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.6.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Collections.Immutable/7.0.0": {"sha512": "dQPcs0U1IKnBdRDBkrCTi1FoajSTBzLcVTpjO4MBCMC7f4pDOIPzgBoX8JjG7X6uZRJ8EBxsi8+DR1JuwjnzOQ==", "type": "package", "path": "system.collections.immutable/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/net7.0/System.Collections.Immutable.dll", "lib/net7.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.7.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition/7.0.0": {"sha512": "tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "type": "package", "path": "system.composition/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.targets", "lib/net461/_._", "lib/netcoreapp2.0/_._", "lib/netstandard2.0/_._", "system.composition.7.0.0.nupkg.sha512", "system.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.AttributedModel/7.0.0": {"sha512": "2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "type": "package", "path": "system.composition.attributedmodel/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.AttributedModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.AttributedModel.targets", "lib/net462/System.Composition.AttributedModel.dll", "lib/net462/System.Composition.AttributedModel.xml", "lib/net6.0/System.Composition.AttributedModel.dll", "lib/net6.0/System.Composition.AttributedModel.xml", "lib/net7.0/System.Composition.AttributedModel.dll", "lib/net7.0/System.Composition.AttributedModel.xml", "lib/netstandard2.0/System.Composition.AttributedModel.dll", "lib/netstandard2.0/System.Composition.AttributedModel.xml", "system.composition.attributedmodel.7.0.0.nupkg.sha512", "system.composition.attributedmodel.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Convention/7.0.0": {"sha512": "IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "type": "package", "path": "system.composition.convention/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Convention.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Convention.targets", "lib/net462/System.Composition.Convention.dll", "lib/net462/System.Composition.Convention.xml", "lib/net6.0/System.Composition.Convention.dll", "lib/net6.0/System.Composition.Convention.xml", "lib/net7.0/System.Composition.Convention.dll", "lib/net7.0/System.Composition.Convention.xml", "lib/netstandard2.0/System.Composition.Convention.dll", "lib/netstandard2.0/System.Composition.Convention.xml", "system.composition.convention.7.0.0.nupkg.sha512", "system.composition.convention.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Hosting/7.0.0": {"sha512": "eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "type": "package", "path": "system.composition.hosting/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Hosting.targets", "lib/net462/System.Composition.Hosting.dll", "lib/net462/System.Composition.Hosting.xml", "lib/net6.0/System.Composition.Hosting.dll", "lib/net6.0/System.Composition.Hosting.xml", "lib/net7.0/System.Composition.Hosting.dll", "lib/net7.0/System.Composition.Hosting.xml", "lib/netstandard2.0/System.Composition.Hosting.dll", "lib/netstandard2.0/System.Composition.Hosting.xml", "system.composition.hosting.7.0.0.nupkg.sha512", "system.composition.hosting.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Runtime/7.0.0": {"sha512": "aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "type": "package", "path": "system.composition.runtime/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.Runtime.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.Runtime.targets", "lib/net462/System.Composition.Runtime.dll", "lib/net462/System.Composition.Runtime.xml", "lib/net6.0/System.Composition.Runtime.dll", "lib/net6.0/System.Composition.Runtime.xml", "lib/net7.0/System.Composition.Runtime.dll", "lib/net7.0/System.Composition.Runtime.xml", "lib/netstandard2.0/System.Composition.Runtime.dll", "lib/netstandard2.0/System.Composition.Runtime.xml", "system.composition.runtime.7.0.0.nupkg.sha512", "system.composition.runtime.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.TypedParts/7.0.0": {"sha512": "ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "type": "package", "path": "system.composition.typedparts/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Composition.TypedParts.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Composition.TypedParts.targets", "lib/net462/System.Composition.TypedParts.dll", "lib/net462/System.Composition.TypedParts.xml", "lib/net6.0/System.Composition.TypedParts.dll", "lib/net6.0/System.Composition.TypedParts.xml", "lib/net7.0/System.Composition.TypedParts.dll", "lib/net7.0/System.Composition.TypedParts.xml", "lib/netstandard2.0/System.Composition.TypedParts.dll", "lib/netstandard2.0/System.Composition.TypedParts.xml", "system.composition.typedparts.7.0.0.nupkg.sha512", "system.composition.typedparts.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/9.0.9": {"sha512": "wpsUfnyv8E5K4WQaok6weewvAbQhcLwXFcHBm5U0gdEaBs85N//ssuYvRPFWwz2rO/9/DFP3A1sGMzUFBj8y3w==", "type": "package", "path": "system.diagnostics.eventlog/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/net9.0/System.Diagnostics.EventLog.dll", "lib/net9.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.9.0.9.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Pipelines/7.0.0": {"sha512": "jRn6JYnNPW6xgQazROBLSfpdoczRw694vO5kKvMcNnpXuolEixUyw6IBuBs2Y2mlSX/LdLvyyWmfXhaI3ND1Yg==", "type": "package", "path": "system.io.pipelines/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/net7.0/System.IO.Pipelines.dll", "lib/net7.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.7.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Metadata/7.0.0": {"sha512": "MclTG61lsD9sYdpNz9xsKBzjsmsfCtcMZYXz/IUr2zlhaTaABonlr1ESeompTgM+Xk+IwtGYU7/voh3YWB/fWw==", "type": "package", "path": "system.reflection.metadata/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Reflection.Metadata.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Reflection.Metadata.targets", "lib/net462/System.Reflection.Metadata.dll", "lib/net462/System.Reflection.Metadata.xml", "lib/net6.0/System.Reflection.Metadata.dll", "lib/net6.0/System.Reflection.Metadata.xml", "lib/net7.0/System.Reflection.Metadata.dll", "lib/net7.0/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "system.reflection.metadata.7.0.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/9.0.9": {"sha512": "NEnpppwq67fRz/OvQRxsEMgetDJsxlxpEsAFO/4PZYbAyAMd4Ol6KS7phc8uDoKPsnbdzRLKobpX303uQwCqdg==", "type": "package", "path": "system.text.json/9.0.9", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.9.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Channels/7.0.0": {"sha512": "qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "type": "package", "path": "system.threading.channels/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.Channels.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.Channels.targets", "lib/net462/System.Threading.Channels.dll", "lib/net462/System.Threading.Channels.xml", "lib/net6.0/System.Threading.Channels.dll", "lib/net6.0/System.Threading.Channels.xml", "lib/net7.0/System.Threading.Channels.dll", "lib/net7.0/System.Threading.Channels.xml", "lib/netstandard2.0/System.Threading.Channels.dll", "lib/netstandard2.0/System.Threading.Channels.xml", "lib/netstandard2.1/System.Threading.Channels.dll", "lib/netstandard2.1/System.Threading.Channels.xml", "system.threading.channels.7.0.0.nupkg.sha512", "system.threading.channels.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0-windows10.0.19041": ["CommunityToolkit.Mvvm >= 8.4.0", "Microsoft.EntityFrameworkCore.Sqlite >= 9.0.9", "Microsoft.EntityFrameworkCore.Tools >= 9.0.9", "Microsoft.Extensions.Hosting >= 9.0.9", "Microsoft.Web.WebView2 >= 1.*", "Microsoft.Windows.SDK.BuildTools >= 10.*", "Microsoft.WindowsAppSDK >= 1.*"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Software3\\Dinpresto\\DinPresto.csproj", "projectName": "DinPresto", "projectPath": "D:\\Software3\\Dinpresto\\DinPresto.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Software3\\Dinpresto\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows10.0.19041.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows10.0.19041": {"targetAlias": "net9.0-windows10.0.19041.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.9, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.9, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.9, )"}, "Microsoft.Web.WebView2": {"target": "Package", "version": "[1.*, )"}, "Microsoft.Windows.SDK.BuildTools": {"target": "Package", "version": "[10.*, )"}, "Microsoft.WindowsAppSDK": {"target": "Package", "version": "[1.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.57, 10.0.19041.57]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}}