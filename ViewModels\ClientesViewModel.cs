using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DinPresto.Models;
using DinPresto.Data;
using Microsoft.EntityFrameworkCore;
using System.Collections.ObjectModel;

namespace DinPresto.ViewModels
{
    public partial class ClientesViewModel : BaseViewModel
    {
        private readonly DinPrestoContext _context;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private TipoDocumento? selectedTipoDocumento;

        [ObservableProperty]
        private Cliente? selectedCliente;

        [ObservableProperty]
        private bool isEditMode;

        [ObservableProperty]
        private bool showClienteForm;

        // Propiedades para el formulario de cliente
        [ObservableProperty]
        private string nombre = string.Empty;

        [ObservableProperty]
        private string apellidos = string.Empty;

        [ObservableProperty]
        private string documentoIdentidad = string.Empty;

        [ObservableProperty]
        private TipoDocumento tipoDocumento = TipoDocumento.Cedula;

        [ObservableProperty]
        private string email = string.Empty;

        [ObservableProperty]
        private string telefono = string.Empty;

        [ObservableProperty]
        private string direccion = string.Empty;

        [ObservableProperty]
        private bool activo = true;

        public ObservableCollection<Cliente> Clientes { get; } = new();
        public ObservableCollection<Cliente> ClientesFiltrados { get; } = new();

        public ClientesViewModel(DinPrestoContext context)
        {
            _context = context;
            Title = "Gestión de Clientes";
            
            // Cargar datos iniciales
            _ = LoadClientesAsync();
        }

        [RelayCommand]
        private async Task LoadClientesAsync()
        {
            await ExecuteAsync(async () =>
            {
                var clientes = await _context.Clientes
                    .Include(c => c.Prestamos)
                    .OrderBy(c => c.Nombre)
                    .ThenBy(c => c.Apellidos)
                    .ToListAsync();

                Clientes.Clear();
                ClientesFiltrados.Clear();

                foreach (var cliente in clientes)
                {
                    Clientes.Add(cliente);
                    ClientesFiltrados.Add(cliente);
                }

            }, "Cargando clientes...");
        }

        [RelayCommand]
        private void SearchClientes()
        {
            ClientesFiltrados.Clear();

            var query = Clientes.AsEnumerable();

            // Filtrar por texto de búsqueda
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchLower = SearchText.ToLower();
                query = query.Where(c => 
                    c.NombreCompleto.ToLower().Contains(searchLower) ||
                    c.DocumentoIdentidad.ToLower().Contains(searchLower) ||
                    (!string.IsNullOrEmpty(c.Telefono) && c.Telefono.Contains(SearchText)) ||
                    (!string.IsNullOrEmpty(c.Email) && c.Email.ToLower().Contains(searchLower)));
            }

            // Filtrar por tipo de documento
            if (SelectedTipoDocumento.HasValue)
            {
                query = query.Where(c => c.TipoDocumento == SelectedTipoDocumento.Value);
            }

            foreach (var cliente in query)
            {
                ClientesFiltrados.Add(cliente);
            }
        }

        [RelayCommand]
        private void ShowNuevoClienteForm()
        {
            ClearForm();
            IsEditMode = false;
            ShowClienteForm = true;
        }

        [RelayCommand]
        private void ShowEditClienteForm(Cliente cliente)
        {
            if (cliente == null) return;

            SelectedCliente = cliente;
            LoadClienteToForm(cliente);
            IsEditMode = true;
            ShowClienteForm = true;
        }

        [RelayCommand]
        private async Task SaveClienteAsync()
        {
            if (!ValidateForm()) return;

            await ExecuteAsync(async () =>
            {
                if (IsEditMode && SelectedCliente != null)
                {
                    // Actualizar cliente existente
                    UpdateClienteFromForm(SelectedCliente);
                    SelectedCliente.FechaUltimaModificacion = DateTime.Now;
                    _context.Clientes.Update(SelectedCliente);
                }
                else
                {
                    // Crear nuevo cliente
                    var nuevoCliente = new Cliente
                    {
                        Nombre = Nombre.Trim(),
                        Apellidos = Apellidos.Trim(),
                        DocumentoIdentidad = DocumentoIdentidad.Trim(),
                        TipoDocumento = TipoDocumento,
                        Email = string.IsNullOrWhiteSpace(Email) ? null : Email.Trim(),
                        Telefono = string.IsNullOrWhiteSpace(Telefono) ? null : Telefono.Trim(),
                        Direccion = string.IsNullOrWhiteSpace(Direccion) ? null : Direccion.Trim(),
                        Activo = Activo,
                        FechaRegistro = DateTime.Now
                    };

                    _context.Clientes.Add(nuevoCliente);
                }

                await _context.SaveChangesAsync();
                ShowClienteForm = false;
                await LoadClientesAsync();

            }, IsEditMode ? "Actualizando cliente..." : "Guardando cliente...");
        }

        [RelayCommand]
        private async Task DeleteClienteAsync(Cliente cliente)
        {
            if (cliente == null) return;

            // Verificar si el cliente tiene préstamos activos
            var tienePrestamosActivos = await _context.Prestamos
                .AnyAsync(p => p.ClienteId == cliente.Id && p.Estado == EstadoPrestamo.Activo);

            if (tienePrestamosActivos)
            {
                AddError("No se puede eliminar el cliente porque tiene préstamos activos.");
                return;
            }

            await ExecuteAsync(async () =>
            {
                // En lugar de eliminar físicamente, marcamos como inactivo
                cliente.Activo = false;
                _context.Clientes.Update(cliente);
                await _context.SaveChangesAsync();
                await LoadClientesAsync();

            }, "Eliminando cliente...");
        }

        [RelayCommand]
        private void CancelEdit()
        {
            ShowClienteForm = false;
            ClearForm();
        }

        [RelayCommand]
        private async Task ViewClienteDetailsAsync(Cliente cliente)
        {
            if (cliente == null) return;

            SelectedCliente = cliente;
            // Aquí se podría navegar a una página de detalles del cliente
            // o mostrar un diálogo con información detallada
        }

        private void ClearForm()
        {
            Nombre = string.Empty;
            Apellidos = string.Empty;
            DocumentoIdentidad = string.Empty;
            TipoDocumento = TipoDocumento.Cedula;
            Email = string.Empty;
            Telefono = string.Empty;
            Direccion = string.Empty;
            Activo = true;
            SelectedCliente = null;
        }

        private void LoadClienteToForm(Cliente cliente)
        {
            Nombre = cliente.Nombre;
            Apellidos = cliente.Apellidos;
            DocumentoIdentidad = cliente.DocumentoIdentidad;
            TipoDocumento = cliente.TipoDocumento;
            Email = cliente.Email ?? string.Empty;
            Telefono = cliente.Telefono ?? string.Empty;
            Direccion = cliente.Direccion ?? string.Empty;
            Activo = cliente.Activo;
        }

        private void UpdateClienteFromForm(Cliente cliente)
        {
            cliente.Nombre = Nombre.Trim();
            cliente.Apellidos = Apellidos.Trim();
            cliente.DocumentoIdentidad = DocumentoIdentidad.Trim();
            cliente.TipoDocumento = TipoDocumento;
            cliente.Email = string.IsNullOrWhiteSpace(Email) ? null : Email.Trim();
            cliente.Telefono = string.IsNullOrWhiteSpace(Telefono) ? null : Telefono.Trim();
            cliente.Direccion = string.IsNullOrWhiteSpace(Direccion) ? null : Direccion.Trim();
            cliente.Activo = Activo;
        }

        private bool ValidateForm()
        {
            ClearErrors();

            if (string.IsNullOrWhiteSpace(Nombre))
                AddError("El nombre es obligatorio.");

            if (string.IsNullOrWhiteSpace(Apellidos))
                AddError("Los apellidos son obligatorios.");

            if (string.IsNullOrWhiteSpace(DocumentoIdentidad))
                AddError("El documento de identidad es obligatorio.");

            // Validar formato de email si se proporciona
            if (!string.IsNullOrWhiteSpace(Email) && !IsValidEmail(Email))
                AddError("El formato del email no es válido.");

            // Verificar que el documento no esté duplicado
            if (!string.IsNullOrWhiteSpace(DocumentoIdentidad))
            {
                var documentoExiste = _context.Clientes
                    .Any(c => c.DocumentoIdentidad == DocumentoIdentidad.Trim() && 
                             (SelectedCliente == null || c.Id != SelectedCliente.Id));

                if (documentoExiste)
                    AddError("Ya existe un cliente con este documento de identidad.");
            }

            return !HasErrors;
        }

        private static bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        // Propiedades calculadas para estadísticas
        public int TotalClientes => Clientes.Count;
        public int ClientesActivos => Clientes.Count(c => c.Activo);
        public int ClientesInactivos => Clientes.Count(c => !c.Activo);
        public int ClientesConPrestamos => Clientes.Count(c => c.TotalPrestamos > 0);
    }
}
